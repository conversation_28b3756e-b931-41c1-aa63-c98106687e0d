import {Component, OnInit} from '@angular/core';
import {AppMainComponent} from './app.main.component';
import {Router} from '@angular/router';
import {TranslateService} from '@ngx-translate/core';
import {DashboardPageComponent} from '../../pages/dashboard/dashboard-page/dashboard-page.component';
import {ForumPageComponent} from '../../pages/forum/forum-page/forum-page.component';
import {
  MachinePlanListPageComponent
} from "../../pages/machine/plan-phase/machine-plan-list-page/machine-plan-list-page.component";
import {ProductPageComponent} from "../../pages/product/product-page/product-page.component";
import {
  MachineBuildListPageComponent
} from '../../pages/machine/build-phase/machine-build-list-page/machine-build-list-page.component';
import {
  MachineRunListPageComponent
} from '../../pages/machine/run-phase/machine-run-list-page/machine-run-list-page.component';
import {
  AdministrationOverviewPageComponent
} from '../../pages/administrator/administration-overview-page/administration-overview-page.component';
import {KnowledgePageComponent} from '../../pages/knowledge/knowledge-page/knowledge-page.component';
import { CatalogPageComponent } from 'src/app/pages/catalog/catalog-page/catalog-page.component';

@Component({
  selector: 'app-sidebar',
  templateUrl: './app.sidebar.component.html',
  styleUrls: ['../../../assets/layout/css/navbar.scss']
})
export class AppSideBarComponent implements OnInit {
  public menuItems: any[];

  constructor(public app: AppMainComponent, public router: Router, public translateService: TranslateService) {
  }

  ngOnInit() {
    this.menuItems = [
      {
        label: this.translateService.instant('Dashboard.page.home'),
        icon: 'pi pi-fw pi-home',
        routerLink: [DashboardPageComponent.ROUTE],
        feature: 'viewDashboard'
      },
      {
        label: this.translateService.instant('Product.title'),
        icon: 'pi pi-fw pi-palette',
        routerLink: [ProductPageComponent.ROUTE],
        feature: 'viewProducts'
      },
      {
        label: this.translateService.instant('Plan.title'),
        icon: 'pi pi-fw pi-compass',
        routerLink: [MachinePlanListPageComponent.ROUTE],
        feature: 'viewPlanPhaseMachineList'
      }, {
        label: this.translateService.instant('Build.title'),
        icon: 'pi pi-fw pi-car',
        routerLink: [MachineBuildListPageComponent.ROUTE],
        feature: 'viewBuildPhaseMachineList'
      }, {
        label: this.translateService.instant('Run.title'),
        icon: 'pi pi-fw pi-play',
        routerLink: [MachineRunListPageComponent.ROUTE],
        feature: 'viewRunPhaseMachineList'
      },
      {
        label: this.translateService.instant('general.settings'),
        icon: 'pi pi-fw pi-cog',
        routerLink: [AdministrationOverviewPageComponent.ROUTE],
        feature: 'viewAdministrationOverview'
      },
      {
        label: 'Notes',
        icon: 'pi pi-fw pi-file-o',
        routerLink: ['/notes']
      },
      {
        label: this.translateService.instant('Knowledge.toKnow'),
        icon: 'pi pi-fw pi-paperclip',
        routerLink: [KnowledgePageComponent.ROUTE],
        feature: 'viewKnowledge'
      },
      {
        label: this.translateService.instant('Knowledge.forumTitle'),
        icon: 'pi pi-fw pi-book',
        routerLink: [ForumPageComponent.ROUTE],
        feature: 'viewForum'
      },
      {
        label: this.translateService.instant('catalog.title'),
        icon: 'pi pi-fw pi-folder-open',
        routerLink: [CatalogPageComponent.ROUTE],
        feature: 'viewForum'
      }
    ];
  }

}
