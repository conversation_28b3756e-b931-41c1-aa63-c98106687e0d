package de.iotiq.cotwinbackend.repository;

import de.iotiq.cotwinbackend.domain.Note;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the Note entity.
 */
@Repository
public interface NoteRepository extends JpaRepository<Note, Long>, JpaSpecificationExecutor<Note> {

    /**
     * Find notes by completion status.
     */
    Page<Note> findByCompleted(Boolean completed, Pageable pageable);

    /**
     * Find notes by priority.
     */
    Page<Note> findByPriority(Note.NotePriority priority, Pageable pageable);

    /**
     * Find notes by category.
     */
    Page<Note> findByCategoryIgnoreCaseContaining(String category, Pageable pageable);

    /**
     * Search notes by title or content.
     */
    @Query("SELECT n FROM Note n WHERE " +
           "LOWER(n.title) LIKE LOWER(CONCAT('%', :query, '%')) OR " +
           "LOWER(n.content) LIKE LOWER(CONCAT('%', :query, '%'))")
    Page<Note> findByTitleOrContentContaining(@Param("query") String query, Pageable pageable);

    /**
     * Find notes by created by user.
     */
    Page<Note> findByCreatedBy(String createdBy, Pageable pageable);

    /**
     * Count notes by completion status.
     */
    long countByCompleted(Boolean completed);

    /**
     * Count notes by priority.
     */
    long countByPriority(Note.NotePriority priority);

    /**
     * Find all categories.
     */
    @Query("SELECT DISTINCT n.category FROM Note n WHERE n.category IS NOT NULL ORDER BY n.category")
    List<String> findAllCategories();
}
