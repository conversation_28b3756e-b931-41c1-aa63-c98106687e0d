.note-list-container {
  padding: 20px;
}

.stats-card {
  border: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease-in-out;
}

.stats-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.stats-card .card-body h3 {
  font-size: 2rem;
  font-weight: bold;
  margin-bottom: 0.5rem;
}

.note-card {
  border: 1px solid #e0e0e0;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.note-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  border-color: #007bff;
}

.note-card.completed {
  opacity: 0.8;
  background-color: #f8f9fa;
}

.note-card.completed .card-title {
  text-decoration: line-through;
}

.note-card .card-header {
  background-color: transparent;
  border-bottom: 1px solid #e0e0e0;
  padding: 0.75rem 1rem;
}

.note-card .card-title {
  font-size: 1rem;
  font-weight: 600;
  color: #333;
  margin-bottom: 0.5rem;
}

.note-card .card-text {
  font-size: 0.9rem;
  line-height: 1.4;
  color: #666;
  margin-bottom: 1rem;
}

.note-meta {
  border-top: 1px solid #f0f0f0;
  padding-top: 0.5rem;
  margin-top: 0.5rem;
}

.badge-danger {
  background-color: #dc3545;
  color: white;
}

.badge-warning {
  background-color: #ffc107;
  color: #212529;
}

.badge-success {
  background-color: #28a745;
  color: white;
}

.badge-secondary {
  background-color: #6c757d;
  color: white;
}

.badge-light {
  background-color: #f8f9fa;
  color: #495057;
  border: 1px solid #dee2e6;
}

.card {
  box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  border: 1px solid rgba(0, 0, 0, 0.125);
}

.form-control:focus {
  border-color: #007bff;
  box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.pagination .page-link {
  color: #007bff;
}

.pagination .page-item.active .page-link {
  background-color: #007bff;
  border-color: #007bff;
}

.spinner-border {
  color: #007bff;
}

.empty-state {
  padding: 2rem;
}

.empty-state i {
  display: block;
  margin: 0 auto;
}

.btn-group .btn {
  flex: 1;
}

.btn-group .btn:not(:last-child) {
  border-right: none;
}

.btn-group .btn:focus {
  z-index: 3;
}

// Priority icons and colors
.badge {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

// Responsive design
@media (max-width: 768px) {
  .note-list-container {
    padding: 10px;
  }
  
  .stats-card .card-body h3 {
    font-size: 1.5rem;
  }
  
  .note-card .card-title {
    font-size: 0.9rem;
  }
  
  .note-card .card-text {
    font-size: 0.8rem;
  }
  
  .btn-group .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
  }
}

// Animation for completion toggle
.note-card {
  transition: all 0.3s ease;
}

.note-card.completed {
  animation: completeNote 0.5s ease-in-out;
}

@keyframes completeNote {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}
