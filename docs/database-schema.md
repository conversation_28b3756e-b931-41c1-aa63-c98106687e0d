# CoTwin Database Schema Documentation

## Overview

The CoTwin database uses PostgreSQL and follows a normalized relational design with proper foreign key relationships and constraints. The schema is managed through Flyway migrations to ensure consistent deployments.

## Core Entities

### User Management

#### `users` Table
```sql
CREATE TABLE users (
    id BIGSERIAL PRIMARY KEY,
    login VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(60) NOT NULL,
    first_name VA<PERSON><PERSON><PERSON>(50),
    last_name VA<PERSON><PERSON><PERSON>(50),
    email VARCHAR(254) UNIQUE,
    image_url VARCHAR(256),
    activated BOOLEAN DEFAULT FALSE,
    lang_key VARCHAR(10),
    activation_key VARCHAR(20),
    reset_key VARCHAR(20),
    reset_date TIMESTAMP,
    created_by <PERSON><PERSON><PERSON><PERSON>(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by <PERSON><PERSON><PERSON><PERSON>(50),
    last_modified_date TIMESTAMP
);
```

**Purpose**: Stores user authentication and profile information
**Key Features**:
- Unique login and email constraints
- Password hashing for security
- Account activation workflow
- Password reset functionality
- Audit trail fields

#### `user_authority` Table
```sql
CREATE TABLE user_authority (
    user_id BIGINT NOT NULL,
    authority_name VARCHAR(50) NOT NULL,
    PRIMARY KEY (user_id, authority_name),
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (authority_name) REFERENCES authority(name)
);
```

**Purpose**: Many-to-many relationship between users and roles
**Roles**: ADMIN, MANUFACTURER, USER

### Company Management

#### `company` Table
```sql
CREATE TABLE company (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    website VARCHAR(255),
    -- Embedded contact person fields
    first_name VARCHAR(255),
    last_name VARCHAR(255),
    phone_number VARCHAR(255),
    email VARCHAR(255),
    web_page VARCHAR(255),
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP
);
```

**Purpose**: Stores company information and embedded contact person
**Key Features**:
- Company profile information
- Embedded contact person (legacy design)
- Full audit trail

### Machine Management

#### `machine` Table
```sql
CREATE TABLE machine (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    machine_phase VARCHAR(50),
    machine_status VARCHAR(50),
    company_id BIGINT,
    responsible_user_id BIGINT,
    template_id BIGINT,
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP,
    -- Foreign keys
    FOREIGN KEY (company_id) REFERENCES company(id),
    FOREIGN KEY (responsible_user_id) REFERENCES users(id),
    FOREIGN KEY (template_id) REFERENCES machine(id)
);
```

**Purpose**: Core entity for managing industrial machines
**Key Features**:
- Machine lifecycle phases (PlanPhase, BuildPhase, etc.)
- Status tracking (NEWLY_ADDED, CONFIGURED, etc.)
- Company association
- Responsible user assignment
- Template-based machine creation
- Self-referencing for template hierarchy

### Person Management (New Feature)

#### `person` Table
```sql
CREATE TABLE person (
    id BIGSERIAL PRIMARY KEY,
    first_name VARCHAR(50) NOT NULL,
    last_name VARCHAR(50) NOT NULL,
    email VARCHAR(254) UNIQUE,
    phone_number VARCHAR(20),
    mobile_number VARCHAR(20),
    web_page VARCHAR(255),
    job_title VARCHAR(100),
    department VARCHAR(50),
    birth_date DATE,
    notes VARCHAR(500),
    active BOOLEAN DEFAULT TRUE,
    -- Embedded address fields
    street VARCHAR(255),
    postcode VARCHAR(10),
    city VARCHAR(100),
    country VARCHAR(100),
    -- Relationships
    company_id BIGINT,
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP,
    -- Foreign keys
    FOREIGN KEY (company_id) REFERENCES company(id)
);
```

**Purpose**: Manages contacts, team members, and stakeholders
**Key Features**:
- Comprehensive contact information
- Job title and department tracking
- Company association
- Embedded address information
- Active/inactive status
- Full audit trail

### Forum System

#### `thread` Table
```sql
CREATE TABLE thread (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255) NOT NULL,
    body TEXT,
    votes INTEGER DEFAULT 0,
    created_by_id BIGINT,
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP,
    -- Foreign keys
    FOREIGN KEY (created_by_id) REFERENCES users(id)
);
```

#### `post` Table
```sql
CREATE TABLE post (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(255),
    body TEXT NOT NULL,
    votes INTEGER DEFAULT 0,
    thread_id BIGINT NOT NULL,
    created_by_id BIGINT,
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP,
    -- Foreign keys
    FOREIGN KEY (thread_id) REFERENCES thread(id),
    FOREIGN KEY (created_by_id) REFERENCES users(id)
);
```

**Purpose**: Collaborative discussion system
**Key Features**:
- Hierarchical thread-post structure
- Voting system for content quality
- User attribution
- Full audit trail

### Tagging System

#### `tag` Table
```sql
CREATE TABLE tag (
    id BIGSERIAL PRIMARY KEY,
    name VARCHAR(255) NOT NULL UNIQUE,
    color VARCHAR(7), -- Hex color code
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP
);
```

#### Junction Tables
```sql
-- Machine-Tag relationship
CREATE TABLE machine_tag (
    machine_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    PRIMARY KEY (machine_id, tag_id),
    FOREIGN KEY (machine_id) REFERENCES machine(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
);

-- Person-Tag relationship
CREATE TABLE person_tag (
    person_id BIGINT NOT NULL,
    tag_id BIGINT NOT NULL,
    PRIMARY KEY (person_id, tag_id),
    FOREIGN KEY (person_id) REFERENCES person(id) ON DELETE CASCADE,
    FOREIGN KEY (tag_id) REFERENCES tag(id) ON DELETE CASCADE
);
```

**Purpose**: Flexible categorization system
**Key Features**:
- Reusable tags across entities
- Color coding for visual organization
- Many-to-many relationships
- Cascade delete for cleanup

## Indexes and Performance

### Primary Indexes
- All tables have BIGSERIAL primary keys
- Unique constraints on login, email fields
- Composite primary keys for junction tables

### Secondary Indexes
```sql
-- User indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_login ON users(login);

-- Company indexes
CREATE INDEX idx_company_name ON company(name);

-- Machine indexes
CREATE INDEX idx_machine_company_id ON machine(company_id);
CREATE INDEX idx_machine_responsible_user_id ON machine(responsible_user_id);
CREATE INDEX idx_machine_name ON machine(name);

-- Person indexes
CREATE INDEX idx_person_email ON person(email);
CREATE INDEX idx_person_company_id ON person(company_id);
CREATE INDEX idx_person_last_name ON person(last_name);
CREATE INDEX idx_person_first_name ON person(first_name);

-- Forum indexes
CREATE INDEX idx_thread_created_by_id ON thread(created_by_id);
CREATE INDEX idx_post_thread_id ON post(thread_id);
CREATE INDEX idx_post_created_by_id ON post(created_by_id);

-- Tag indexes
CREATE INDEX idx_tag_name ON tag(name);
```

## Data Integrity

### Foreign Key Constraints
- All relationships enforced with foreign keys
- Cascade delete for junction tables
- Restrict delete for core entities to prevent data loss

### Check Constraints
- Email format validation
- Enum value validation for machine phases/statuses
- Date range validation where applicable

### Unique Constraints
- User login and email uniqueness
- Tag name uniqueness
- Person email uniqueness (when provided)

## Audit Trail

All core entities include audit fields:
- `created_by`: User who created the record
- `created_date`: Timestamp of creation
- `last_modified_by`: User who last modified the record
- `last_modified_date`: Timestamp of last modification

This provides complete traceability of data changes.

## Migration Strategy

### Flyway Migrations
- Sequential versioning (V1, V2, V3, etc.)
- Repeatable migrations for views and functions
- Rollback scripts for critical changes
- Data migration scripts for schema changes

### Version History
- V1-V5: Initial schema creation
- V6-V10: Forum system implementation
- V11-V14: Enhanced machine management
- V15-V16: Person management feature (latest)

## Extension Points

### Adding New Entities
1. Create migration script
2. Define JPA entity with proper annotations
3. Add repository interface
4. Implement service layer
5. Create REST endpoints
6. Add frontend components

### Modifying Existing Schema
1. Create new migration script
2. Update JPA entity
3. Update related services and controllers
4. Test data migration
5. Update documentation

This schema design provides a solid foundation for the CoTwin platform while allowing for future extensions and modifications.
