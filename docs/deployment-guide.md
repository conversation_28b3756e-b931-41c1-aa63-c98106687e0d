# CoTwin Deployment Guide

## Overview

This guide covers deployment strategies for the CoTwin platform, from local development to production environments.

## Prerequisites

### System Requirements

**Minimum Requirements:**
- CPU: 2 cores
- RAM: 4GB
- Storage: 20GB
- OS: Linux (Ubuntu 20.04+), Windows 10+, macOS 10.15+

**Recommended for Production:**
- CPU: 4+ cores
- RAM: 8GB+
- Storage: 100GB+ SSD
- OS: Linux (Ubuntu 20.04+ or CentOS 8+)

### Software Dependencies

- **Java**: OpenJDK 17 or higher
- **Node.js**: 16.x or higher
- **PostgreSQL**: 11.x or higher
- **Elasticsearch**: 7.x (optional, for search features)
- **Docker**: 20.x+ (for containerized deployment)
- **Docker Compose**: 1.29+ (for multi-container setup)

## Local Development Deployment

### Manual Setup

1. **Database Setup**
   ```bash
   # Install PostgreSQL
   sudo apt-get install postgresql postgresql-contrib
   
   # Create database and user
   sudo -u postgres psql
   CREATE DATABASE cotwin;
   CREATE USER cotwin_user WITH PASSWORD 'cotwin_pass';
   GRANT ALL PRIVILEGES ON DATABASE cotwin TO cotwin_user;
   ```

2. **Backend Setup**
   ```bash
   cd cotwin-backend-master
   
   # Configure application-local.yml
   cp src/main/resources/application.yml src/main/resources/application-local.yml
   # Edit database connection settings
   
   # Run the application
   ./gradlew bootRun --args='--spring.profiles.active=local'
   ```

3. **Frontend Setup**
   ```bash
   cd cotwin-frontend-master
   
   # Install dependencies
   npm install
   
   # Start development server
   npm start
   ```

### Docker Compose Setup

```bash
cd cotwin-backend-master
docker-compose up -d
```

This starts:
- PostgreSQL database
- Elasticsearch (optional)
- Backend application
- Frontend application (if configured)

## Production Deployment

### Option 1: Traditional Server Deployment

#### 1. Server Preparation

```bash
# Update system
sudo apt-get update && sudo apt-get upgrade -y

# Install Java 17
sudo apt-get install openjdk-17-jdk

# Install Node.js
curl -fsSL https://deb.nodesource.com/setup_16.x | sudo -E bash -
sudo apt-get install -y nodejs

# Install PostgreSQL
sudo apt-get install postgresql postgresql-contrib

# Install Nginx (reverse proxy)
sudo apt-get install nginx
```

#### 2. Database Setup

```bash
# Configure PostgreSQL
sudo -u postgres psql
CREATE DATABASE cotwin_prod;
CREATE USER cotwin_prod WITH PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE cotwin_prod TO cotwin_prod;

# Configure PostgreSQL for production
sudo nano /etc/postgresql/13/main/postgresql.conf
# Set: shared_buffers = 256MB, effective_cache_size = 1GB

sudo nano /etc/postgresql/13/main/pg_hba.conf
# Configure authentication methods

sudo systemctl restart postgresql
```

#### 3. Backend Deployment

```bash
# Create application user
sudo useradd -r -s /bin/false cotwin

# Create application directory
sudo mkdir -p /opt/cotwin
sudo chown cotwin:cotwin /opt/cotwin

# Build application
cd cotwin-backend-master
./gradlew build -x test

# Copy JAR file
sudo cp build/libs/cotwin-backend-*.jar /opt/cotwin/cotwin-backend.jar

# Create application configuration
sudo nano /opt/cotwin/application-prod.yml
```

**Production Configuration (application-prod.yml):**
```yaml
server:
  port: 8080

spring:
  datasource:
    url: ********************************************
    username: cotwin_prod
    password: ${DATABASE_PASSWORD}
  
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
  
  flyway:
    enabled: true
    locations: classpath:db/migration

jwt:
  secret: ${JWT_SECRET}
  expiration: 86400

logging:
  level:
    de.iotiq.cotwinbackend: INFO
  file:
    name: /var/log/cotwin/application.log

management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
```

#### 4. Systemd Service

```bash
# Create systemd service
sudo nano /etc/systemd/system/cotwin-backend.service
```

**Service Configuration:**
```ini
[Unit]
Description=CoTwin Backend Application
After=network.target postgresql.service

[Service]
Type=simple
User=cotwin
Group=cotwin
WorkingDirectory=/opt/cotwin
ExecStart=/usr/bin/java -jar -Dspring.profiles.active=prod cotwin-backend.jar
Restart=always
RestartSec=10
Environment=DATABASE_PASSWORD=secure_password_here
Environment=JWT_SECRET=your_jwt_secret_key_here

[Install]
WantedBy=multi-user.target
```

```bash
# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable cotwin-backend
sudo systemctl start cotwin-backend
```

#### 5. Frontend Deployment

```bash
# Build frontend
cd cotwin-frontend-master
npm install
npm run build:prod

# Copy to web server directory
sudo cp -r dist/* /var/www/html/cotwin/
sudo chown -R www-data:www-data /var/www/html/cotwin/
```

#### 6. Nginx Configuration

```bash
sudo nano /etc/nginx/sites-available/cotwin
```

**Nginx Configuration:**
```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL configuration
    ssl_certificate /path/to/ssl/certificate.crt;
    ssl_certificate_key /path/to/ssl/private.key;
    
    # Frontend
    location / {
        root /var/www/html/cotwin;
        index index.html;
        try_files $uri $uri/ /index.html;
    }
    
    # Backend API
    location /api/ {
        proxy_pass http://localhost:8080/api/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # WebSocket support (if needed)
    location /ws/ {
        proxy_pass http://localhost:8080/ws/;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
```

```bash
# Enable site
sudo ln -s /etc/nginx/sites-available/cotwin /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### Option 2: Docker Production Deployment

#### 1. Production Docker Compose

```yaml
# docker-compose.prod.yml
version: '3.8'

services:
  postgres:
    image: postgres:13
    environment:
      POSTGRES_DB: cotwin_prod
      POSTGRES_USER: cotwin_prod
      POSTGRES_PASSWORD: ${DATABASE_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backup:/backup
    ports:
      - "5432:5432"
    restart: unless-stopped

  elasticsearch:
    image: elasticsearch:7.17.0
    environment:
      - discovery.type=single-node
      - "ES_JAVA_OPTS=-Xms512m -Xmx512m"
    volumes:
      - elasticsearch_data:/usr/share/elasticsearch/data
    ports:
      - "9200:9200"
    restart: unless-stopped

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      SPRING_PROFILES_ACTIVE: prod
      DATABASE_URL: *******************************************
      DATABASE_USERNAME: cotwin_prod
      DATABASE_PASSWORD: ${DATABASE_PASSWORD}
      JWT_SECRET: ${JWT_SECRET}
      ELASTICSEARCH_URL: http://elasticsearch:9200
    ports:
      - "8080:8080"
    depends_on:
      - postgres
      - elasticsearch
    restart: unless-stopped
    volumes:
      - ./logs:/var/log/cotwin

  frontend:
    build:
      context: ../cotwin-frontend-master
      dockerfile: Dockerfile.prod
    ports:
      - "80:80"
      - "443:443"
    depends_on:
      - backend
    restart: unless-stopped
    volumes:
      - ./ssl:/etc/nginx/ssl

volumes:
  postgres_data:
  elasticsearch_data:
```

#### 2. Production Dockerfile

```dockerfile
# Dockerfile
FROM openjdk:17-jdk-slim

WORKDIR /app

# Copy application JAR
COPY build/libs/cotwin-backend-*.jar app.jar

# Create non-root user
RUN groupadd -r cotwin && useradd -r -g cotwin cotwin
RUN chown cotwin:cotwin app.jar

USER cotwin

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=60s \
  CMD curl -f http://localhost:8080/actuator/health || exit 1

EXPOSE 8080

ENTRYPOINT ["java", "-jar", "app.jar"]
```

#### 3. Deploy with Docker

```bash
# Set environment variables
export DATABASE_PASSWORD=secure_password_here
export JWT_SECRET=your_jwt_secret_key_here

# Build and deploy
docker-compose -f docker-compose.prod.yml up -d --build

# View logs
docker-compose -f docker-compose.prod.yml logs -f
```

## Cloud Deployment

### AWS Deployment

#### Using AWS ECS (Elastic Container Service)

1. **Push images to ECR**
2. **Create ECS cluster**
3. **Define task definitions**
4. **Set up Application Load Balancer**
5. **Configure RDS for PostgreSQL**
6. **Set up CloudWatch for monitoring**

#### Using AWS Elastic Beanstalk

1. **Package application as JAR**
2. **Create Elastic Beanstalk application**
3. **Configure environment variables**
4. **Set up RDS database**
5. **Configure load balancer and auto-scaling**

### Azure Deployment

#### Using Azure Container Instances

1. **Push images to Azure Container Registry**
2. **Create container groups**
3. **Set up Azure Database for PostgreSQL**
4. **Configure Application Gateway**

### Google Cloud Deployment

#### Using Google Cloud Run

1. **Build and push to Container Registry**
2. **Deploy to Cloud Run**
3. **Set up Cloud SQL for PostgreSQL**
4. **Configure load balancer**

## Monitoring and Maintenance

### Health Checks

- **Backend**: `GET /actuator/health`
- **Database**: Connection pool monitoring
- **Elasticsearch**: Cluster health API

### Logging

```bash
# View application logs
sudo journalctl -u cotwin-backend -f

# View Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### Backup Strategy

```bash
# Database backup script
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
pg_dump -h localhost -U cotwin_prod cotwin_prod > /backup/cotwin_backup_$DATE.sql
find /backup -name "cotwin_backup_*.sql" -mtime +7 -delete
```

### Security Considerations

1. **Use HTTPS in production**
2. **Configure firewall rules**
3. **Regular security updates**
4. **Database connection encryption**
5. **JWT secret rotation**
6. **Input validation and sanitization**
7. **Rate limiting**
8. **CORS configuration**

### Performance Tuning

1. **Database optimization**
   - Connection pooling
   - Query optimization
   - Proper indexing

2. **Application optimization**
   - JVM tuning
   - Caching strategies
   - Async processing

3. **Frontend optimization**
   - CDN usage
   - Asset compression
   - Lazy loading

This deployment guide provides comprehensive instructions for deploying CoTwin in various environments, from development to production-ready cloud deployments.
