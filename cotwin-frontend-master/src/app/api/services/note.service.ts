import { Injectable } from '@angular/core';
import { Observable } from 'rxjs';
import { ApiServiceAbstract } from '../../services/api-service.abstract';
import { Pageable } from '../../models/table/Pageable.class';

export interface NoteDto {
  id?: number;
  title: string;
  content?: string;
  priority: NotePriority;
  completed?: boolean;
  category?: string;
  createdDate?: string;
  lastModifiedDate?: string;
  createdBy?: string;
  lastModifiedBy?: string;
  displayTitle?: string;
  priorityIcon?: string;
}

export enum NotePriority {
  HIGH = 'HIGH',
  MEDIUM = 'MEDIUM',
  LOW = 'LOW'
}

export interface SaveNoteRequest {
  title: string;
  content?: string;
  priority?: NotePriority;
  completed?: boolean;
  category?: string;
}

export interface GetNoteRequest {
  title?: string;
  content?: string;
  category?: string;
  priority?: NotePriority;
  completed?: boolean;
  createdBy?: string;
  query?: string;
}

export interface NoteStatsDto {
  totalNotes: number;
  completedNotes: number;
  pendingNotes: number;
  highPriorityNotes: number;
  categories: string[];
}

export interface PagedModelEntityModelNoteDto {
  _embedded?: {
    noteDtoes?: NoteDto[];
  };
  page?: {
    size: number;
    totalElements: number;
    totalPages: number;
    number: number;
  };
}

@Injectable({
  providedIn: 'root'
})
export class NoteService extends ApiServiceAbstract {

  /**
   * Get paginated list of notes with filtering
   */
  public getNoteList(filter: GetNoteRequest, pageable: Pageable): Observable<PagedModelEntityModelNoteDto> {
    let params = { ...filter, ...pageable };
    params = this.removeUndefinedAndNull(params);
    return this.getForPageableObject<PagedModelEntityModelNoteDto>('/notes', params);
  }

  /**
   * Get note by ID
   */
  public getNoteById(id: string, params?: any): Observable<NoteDto> {
    return this.getForObject<NoteDto>(`/notes/${id}`, params);
  }

  /**
   * Create a new note
   */
  public createNote(request: SaveNoteRequest): Observable<NoteDto> {
    const requestFiltered = this.removeUndefinedAndNull(request);
    return this.postForObject<NoteDto>('/notes', requestFiltered, {});
  }

  /**
   * Update an existing note
   */
  public updateNote(id: number, request: SaveNoteRequest): Observable<NoteDto> {
    const requestFiltered = this.removeUndefinedAndNull(request);
    return this.putForObject<NoteDto>(`/notes/${id}`, requestFiltered, {});
  }

  /**
   * Delete a note
   */
  public deleteNote(id: number): Observable<void> {
    return this.delete<void>(`/notes/${id}`, {});
  }

  /**
   * Toggle note completion status
   */
  public toggleNoteCompletion(id: number): Observable<NoteDto> {
    return this.putForObject<NoteDto>(`/notes/${id}/toggle`, null, {});
  }

  /**
   * Get note statistics
   */
  public getNoteStatistics(): Observable<NoteStatsDto> {
    return this.getForObject<NoteStatsDto>('/notes/stats', {});
  }
}
