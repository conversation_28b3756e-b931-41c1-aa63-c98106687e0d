import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { NoteService, NoteDto, GetNoteRequest, NotePriority, NoteStatsDto } from '../../../api/services/note.service';
import { Pageable } from '../../../models/table/Pageable.class';
import { FormBuilder, FormGroup } from '@angular/forms';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';

@Component({
  selector: 'app-note-list',
  templateUrl: './note-list.component.html',
  styleUrls: ['./note-list.component.scss']
})
export class NoteListComponent implements OnInit {

  notes: NoteDto[] = [];
  loading = false;
  totalElements = 0;
  currentPage = 0;
  pageSize = 10;

  // Filter form
  filterForm: FormGroup;
  showFilters = false;

  // Search
  searchQuery = '';

  // Statistics
  stats: NoteStatsDto | null = null;

  // Enums for template
  NotePriority = NotePriority;
  priorities = Object.values(NotePriority);

  constructor(
    private noteService: NoteService,
    private router: Router,
    private fb: FormBuilder
  ) {
    this.filterForm = this.fb.group({
      title: [''],
      content: [''],
      category: [''],
      priority: [null],
      completed: [null],
      query: ['']
    });
  }

  ngOnInit(): void {
    this.loadNotes();
    this.loadStatistics();
    this.setupFilterSubscription();
  }

  private setupFilterSubscription(): void {
    this.filterForm.valueChanges
      .pipe(
        debounceTime(300),
        distinctUntilChanged()
      )
      .subscribe(() => {
        if (this.showFilters) {
          this.currentPage = 0;
          this.loadNotes();
        }
      });
  }

  loadNotes(): void {
    this.loading = true;

    const pageable: Pageable = {
      page: this.currentPage,
      size: this.pageSize,
      sort: ['lastModifiedDate,desc']
    };

    const filter: GetNoteRequest = this.showFilters ? this.filterForm.value : {};

    // Add search query if present
    if (this.searchQuery && this.searchQuery.trim()) {
      filter.query = this.searchQuery;
    }

    this.noteService.getNoteList(filter, pageable).subscribe({
      next: (response) => {
        this.notes = response._embedded?.noteDtoes || [];
        this.totalElements = response.page?.totalElements || 0;
        this.loading = false;
      },
      error: (error) => {
        console.error('Error loading notes:', error);
        this.loading = false;
      }
    });
  }

  private loadStatistics(): void {
    this.noteService.getNoteStatistics().subscribe({
      next: (stats) => {
        this.stats = stats;
      },
      error: (error) => console.error('Error loading statistics:', error)
    });
  }

  onSearch(): void {
    this.currentPage = 0;
    this.showFilters = false;
    this.loadNotes();
  }

  onSearchClear(): void {
    this.searchQuery = '';
    this.currentPage = 0;
    this.loadNotes();
  }

  toggleFilters(): void {
    this.showFilters = !this.showFilters;
    if (!this.showFilters) {
      this.filterForm.reset();
      this.searchQuery = '';
      this.currentPage = 0;
      this.loadNotes();
    }
  }

  clearFilters(): void {
    this.filterForm.reset();
    this.currentPage = 0;
    this.loadNotes();
  }

  onPageChange(page: number): void {
    this.currentPage = page;
    this.loadNotes();
  }

  onPageSizeChange(size: number): void {
    this.pageSize = size;
    this.currentPage = 0;
    this.loadNotes();
  }

  createNote(): void {
    this.router.navigate(['/notes/new']);
  }

  editNote(note: NoteDto): void {
    this.router.navigate(['/notes', note.id, 'edit']);
  }

  viewNote(note: NoteDto): void {
    this.router.navigate(['/notes', note.id]);
  }

  toggleCompletion(note: NoteDto): void {
    this.noteService.toggleNoteCompletion(note.id!).subscribe({
      next: (updatedNote) => {
        // Update the note in the list
        const index = this.notes.findIndex(n => n.id === note.id);
        if (index !== -1) {
          this.notes[index] = updatedNote;
        }
        this.loadStatistics(); // Refresh statistics
      },
      error: (error) => {
        console.error('Error toggling note completion:', error);
        alert('Error updating note. Please try again.');
      }
    });
  }

  deleteNote(note: NoteDto): void {
    if (confirm(`Are you sure you want to delete "${note.title}"?`)) {
      this.noteService.deleteNote(note.id!).subscribe({
        next: () => {
          this.loadNotes();
          this.loadStatistics();
        },
        error: (error) => {
          console.error('Error deleting note:', error);
          alert('Error deleting note. Please try again.');
        }
      });
    }
  }

  getPriorityClass(priority: NotePriority): string {
    switch (priority) {
      case NotePriority.HIGH:
        return 'badge-danger';
      case NotePriority.MEDIUM:
        return 'badge-warning';
      case NotePriority.LOW:
        return 'badge-success';
      default:
        return 'badge-secondary';
    }
  }

  getCompletionClass(completed: boolean): string {
    return completed ? 'badge-success' : 'badge-secondary';
  }

  getCompletionText(completed: boolean): string {
    return completed ? 'Completed' : 'Pending';
  }

  getCompletionPercentage(): number {
    if (!this.stats || this.stats.totalNotes === 0) return 0;
    return Math.round((this.stats.completedNotes / this.stats.totalNotes) * 100);
  }
}
