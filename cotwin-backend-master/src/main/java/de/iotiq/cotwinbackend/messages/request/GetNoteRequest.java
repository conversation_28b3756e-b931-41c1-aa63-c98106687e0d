package de.iotiq.cotwinbackend.messages.request;

import de.iotiq.cotwinbackend.domain.Note;
import lombok.Data;

/**
 * Request DTO for filtering and searching notes.
 */
@Data
public class GetNoteRequest {
    private String title;
    private String content;
    private String category;
    private Note.NotePriority priority;
    private Boolean completed;
    private String createdBy;
    private String query; // For full-text search
}
