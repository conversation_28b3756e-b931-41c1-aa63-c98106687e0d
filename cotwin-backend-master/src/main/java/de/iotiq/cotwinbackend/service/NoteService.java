package de.iotiq.cotwinbackend.service;

import de.iotiq.cotwinbackend.domain.Note;
import de.iotiq.cotwinbackend.messages.request.GetNoteRequest;
import de.iotiq.cotwinbackend.messages.request.SaveNoteRequest;
import de.iotiq.cotwinbackend.messages.response.NoteDTO;
import de.iotiq.cotwinbackend.repository.NoteRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.modelmapper.ModelMapper;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.domain.Specification;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import javax.persistence.criteria.Predicate;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing {@link Note}.
 */
@Service
@Transactional
@RequiredArgsConstructor
@Slf4j
public class NoteService {

    private final NoteRepository noteRepository;
    private final ModelMapper mapper;

    /**
     * Create a new note.
     */
    public NoteDTO createNote(SaveNoteRequest request) {
        log.debug("Request to create Note: {}", request);
        
        Note note = mapper.map(request, Note.class);
        note = noteRepository.save(note);
        return convertToDTO(note);
    }

    /**
     * Update an existing note.
     */
    public NoteDTO updateNote(Long id, SaveNoteRequest request) {
        log.debug("Request to update Note: {} with data: {}", id, request);
        
        Note note = noteRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Note not found with id: " + id));
        
        // Map request to existing note
        mapper.map(request, note);
        note = noteRepository.save(note);
        return convertToDTO(note);
    }

    /**
     * Get all notes with filtering.
     */
    @Transactional(readOnly = true)
    public Page<NoteDTO> searchNotes(GetNoteRequest request, Pageable pageable) {
        log.debug("Request to search Notes: {}", request);
        
        Specification<Note> spec = createSpecification(request);
        Page<Note> page = noteRepository.findAll(spec, pageable);
        
        List<NoteDTO> dtos = page.getContent().stream()
            .map(this::convertToDTO)
            .collect(Collectors.toList());
        
        return new PageImpl<>(dtos, page.getPageable(), page.getTotalElements());
    }

    /**
     * Get one note by id.
     */
    @Transactional(readOnly = true)
    public Optional<NoteDTO> findNote(Long id) {
        log.debug("Request to get Note: {}", id);
        return noteRepository.findById(id)
            .map(this::convertToDTO);
    }

    /**
     * Delete the note by id.
     */
    public void deleteNote(Long id) {
        log.debug("Request to delete Note: {}", id);
        Note note = noteRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Note not found with id: " + id));
        noteRepository.delete(note);
    }

    /**
     * Toggle note completion status.
     */
    public NoteDTO toggleNoteCompletion(Long id) {
        log.debug("Request to toggle completion for Note: {}", id);
        
        Note note = noteRepository.findById(id)
            .orElseThrow(() -> new RuntimeException("Note not found with id: " + id));
        
        note.setCompleted(!note.getCompleted());
        note = noteRepository.save(note);
        return convertToDTO(note);
    }

    /**
     * Get note statistics.
     */
    @Transactional(readOnly = true)
    public NoteStatsDTO getNoteStatistics() {
        log.debug("Request to get Note statistics");
        
        long totalNotes = noteRepository.count();
        long completedNotes = noteRepository.countByCompleted(true);
        long pendingNotes = noteRepository.countByCompleted(false);
        long highPriorityNotes = noteRepository.countByPriority(Note.NotePriority.HIGH);
        
        List<String> categories = noteRepository.findAllCategories();
        
        return new NoteStatsDTO(totalNotes, completedNotes, pendingNotes, highPriorityNotes, categories);
    }

    /**
     * Create JPA Specification for filtering.
     */
    private Specification<Note> createSpecification(GetNoteRequest request) {
        return (root, query, criteriaBuilder) -> {
            List<Predicate> predicates = new ArrayList<>();
            
            if (StringUtils.hasText(request.getTitle())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("title")),
                    "%" + request.getTitle().toLowerCase() + "%"
                ));
            }
            
            if (StringUtils.hasText(request.getContent())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("content")),
                    "%" + request.getContent().toLowerCase() + "%"
                ));
            }
            
            if (StringUtils.hasText(request.getCategory())) {
                predicates.add(criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("category")),
                    "%" + request.getCategory().toLowerCase() + "%"
                ));
            }
            
            if (request.getPriority() != null) {
                predicates.add(criteriaBuilder.equal(root.get("priority"), request.getPriority()));
            }
            
            if (request.getCompleted() != null) {
                predicates.add(criteriaBuilder.equal(root.get("completed"), request.getCompleted()));
            }
            
            if (StringUtils.hasText(request.getCreatedBy())) {
                predicates.add(criteriaBuilder.equal(root.get("createdBy"), request.getCreatedBy()));
            }
            
            // Full-text search across title and content
            if (StringUtils.hasText(request.getQuery())) {
                Predicate titlePredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("title")),
                    "%" + request.getQuery().toLowerCase() + "%"
                );
                Predicate contentPredicate = criteriaBuilder.like(
                    criteriaBuilder.lower(root.get("content")),
                    "%" + request.getQuery().toLowerCase() + "%"
                );
                predicates.add(criteriaBuilder.or(titlePredicate, contentPredicate));
            }
            
            return criteriaBuilder.and(predicates.toArray(new Predicate[0]));
        };
    }

    /**
     * Convert Note entity to DTO.
     */
    private NoteDTO convertToDTO(Note note) {
        NoteDTO dto = mapper.map(note, NoteDTO.class);
        
        // Set computed fields
        dto.setDisplayTitle(note.getDisplayTitle());
        dto.setPriorityIcon(note.getPriorityIcon());
        
        return dto;
    }

    /**
     * DTO for note statistics.
     */
    public static class NoteStatsDTO {
        public final long totalNotes;
        public final long completedNotes;
        public final long pendingNotes;
        public final long highPriorityNotes;
        public final List<String> categories;

        public NoteStatsDTO(long totalNotes, long completedNotes, long pendingNotes, 
                           long highPriorityNotes, List<String> categories) {
            this.totalNotes = totalNotes;
            this.completedNotes = completedNotes;
            this.pendingNotes = pendingNotes;
            this.highPriorityNotes = highPriorityNotes;
            this.categories = categories;
        }
    }
}
