package de.iotiq.cotwinbackend.domain;

import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.search.mapper.pojo.mapping.definition.annotation.FullTextField;

import javax.persistence.Embeddable;

/**
 * Embeddable Person class.
 * Used in Company entity as contactPerson.
 */
@Data
@NoArgsConstructor
@Embeddable
public class Person {
    @FullTextField
    private String firstName;
    @FullTextField
    private String lastName;
    @FullTextField
    private String phoneNumber;
    @FullTextField
    private String email;

    private String webPage;

    // Utility method
    public String getFullName() {
        if (firstName == null && lastName == null) {
            return null;
        }
        return (firstName != null ? firstName : "") + " " + (lastName != null ? lastName : "");
    }
}
