package de.iotiq.cotwinbackend.listeners;

import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASMachineCreateRequest;
import de.iotiq.cotwinbackend.assetadminshell.domain.message.internal.AASMachineUpdateRequest;
import de.iotiq.cotwinbackend.assetadminshell.service.impl.AASMachineServiceImpl;
import de.iotiq.cotwinbackend.domain.listener.event.MachineCreateEvent;
import de.iotiq.cotwinbackend.domain.listener.event.MachineDeleteEvent;
import de.iotiq.cotwinbackend.domain.listener.event.MachineUpdateEvent;
import de.iotiq.cotwinbackend.messages.response.MachineDTO;
import de.iotiq.cotwinbackend.messages.response.TagDTO;
import de.iotiq.cotwinbackend.service.ImageService;
import de.iotiq.cotwinbackend.util.NullHandlerUtil;
import lombok.RequiredArgsConstructor;
import org.modelmapper.ModelMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.context.event.EventListener;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;
import org.springframework.transaction.event.TransactionalEventListener;

import java.util.stream.Collectors;


@Component
@RequiredArgsConstructor
@ConditionalOnProperty(value = "aas.enabled", havingValue = "true", matchIfMissing = true)
public class MachineEventListener {

    Logger logger = LoggerFactory.getLogger(getClass());

    private final AASMachineServiceImpl aasMachineService;
    private final ModelMapper dtoModelMapper;
    private final ImageService imageService;

    @EventListener
    @KafkaListener(id = "machineCreatedConsumer", topics = MachineCreateEvent.TOPIC)
    public void handleMachineCreateEvent(MachineCreateEvent event) {
        logger.info("Caught machine create event {}", event.machine().getId());

        AASMachineCreateRequest aasRequest = mapToAASRequest(event.machine());
        aasMachineService.create(aasRequest);
    }

    @TransactionalEventListener
    @KafkaListener(id = "machineUpdatedConsumer", topics = MachineUpdateEvent.TOPIC)
    public void handleMachineUpdateEvent(MachineUpdateEvent event) {
        logger.info("Caught machine update event {}", event.machine().getId());

        AASMachineUpdateRequest aasRequest = mapToAASRequest(event.machine());
        aasMachineService.update(aasRequest);
    }

    @EventListener
    @KafkaListener(id = "machineDeletedConsumer", topics = MachineDeleteEvent.TOPIC)
    public void handleMachineDeleteEvent(MachineDeleteEvent event) {
        logger.info("Caught machine delete event {}", event.id());

        aasMachineService.delete(event.id());
    }

    private AASMachineUpdateRequest mapToAASRequest(MachineDTO machine) {
        AASMachineUpdateRequest request = dtoModelMapper.map(machine, AASMachineUpdateRequest.class);
        NullHandlerUtil.setIfNotNull(request::setResponsibleUser, () -> machine.getResponsibleUser().getFullName(), machine.getResponsibleUser());
        NullHandlerUtil.setIfNotNull(request::setTemplate, () -> machine.getTemplate().getName(), machine.getTemplate());
        NullHandlerUtil.setIfNotNull(request::setCustomer, () -> machine.getCompany().getName(), machine.getCompany());
        NullHandlerUtil.setIfNotNull(request::setMachineType, () -> machine.getMachineType().getName(), machine.getMachineType());
        NullHandlerUtil.setIfNotNull(request::setMachinePhase, () -> machine.getMachinePhase().stringify(), machine.getMachinePhase());
        NullHandlerUtil.setIfNotNull(request::setOrderDate, () -> machine.getOrder().getOrderDate(), machine.getOrder());
        NullHandlerUtil.setIfNotNull(request::setDeliveryServices, () -> machine.getDelivery().getDeliveryServices(), machine.getDelivery());
        NullHandlerUtil.setIfNotNull(request::setDeliveryDate, () -> machine.getDelivery().getDeliveryDate(), machine.getDelivery());
        NullHandlerUtil.setIfNotNull(request::setImageUrl, () -> imageService.getImageURI(machine.getImageName()).toString(), machine.getImageName());
        request.setTags(machine.getTags().stream().map(TagDTO::getName).collect(Collectors.toSet()));
        return request;
    }
}
