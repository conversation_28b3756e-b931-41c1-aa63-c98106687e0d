-- Create Note table for the simple note management system
-- This demonstrates platform extensibility with a standalone feature

-- Create sequence for note ID generation
CREATE SEQUENCE note_seq_gen START 1 INCREMENT 1;

-- Create the note table
CREATE TABLE note (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content VARCHAR(1000),
    priority VARCHAR(10) DEFAULT 'MEDIUM',
    completed BOOLEAN DEFAULT FALSE,
    category VARCHAR(50),
    
    -- Audit fields
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Create indexes for better performance
CREATE INDEX idx_note_title ON note(title);
CREATE INDEX idx_note_priority ON note(priority);
CREATE INDEX idx_note_completed ON note(completed);
CREATE INDEX idx_note_category ON note(category);
CREATE INDEX idx_note_created_by ON note(created_by);
CREATE INDEX idx_note_created_date ON note(created_date);

-- Insert some sample notes for demonstration
INSERT INTO note (
    title, content, priority, completed, category,
    created_by, created_date, last_modified_by, last_modified_date
) VALUES 
(
    'Welcome to CoTwin Notes', 
    'This is a simple note-taking system that demonstrates the extensibility of the CoTwin platform. You can create, edit, and organize your notes here.',
    'HIGH', 
    FALSE, 
    'Getting Started',
    'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP
),
(
    'Machine Maintenance Reminder', 
    'Remember to schedule regular maintenance for Machine #8 next week. Check oil levels and calibrate sensors.',
    'HIGH', 
    FALSE, 
    'Maintenance',
    'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP
),
(
    'Team Meeting Notes', 
    'Discussed Q4 production targets and new automation initiatives. Action items: Review budget proposals and schedule training sessions.',
    'MEDIUM', 
    TRUE, 
    'Meetings',
    'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP
),
(
    'Software Update Checklist', 
    'Update CoTwin platform to latest version. Test all integrations and verify data migration.',
    'MEDIUM', 
    FALSE, 
    'IT',
    'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP
),
(
    'Lunch Ideas', 
    'Try the new restaurant downtown. Menu looks interesting with good vegetarian options.',
    'LOW', 
    FALSE, 
    'Personal',
    'system', CURRENT_TIMESTAMP, 'system', CURRENT_TIMESTAMP
);

-- Add some additional notes to demonstrate different priorities and categories
INSERT INTO note (
    title, content, priority, completed, category,
    created_by, created_date, last_modified_by, last_modified_date
) VALUES 
(
    'Security Audit', 
    'Conduct quarterly security audit of all systems. Review access logs and update passwords.',
    'HIGH', 
    FALSE, 
    'Security',
    'admin', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP
),
(
    'Training Materials', 
    'Prepare training materials for new employees on CoTwin platform usage.',
    'MEDIUM', 
    TRUE, 
    'Training',
    'admin', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP
),
(
    'Weekend Plans', 
    'Visit the museum and try that new coffee shop. Maybe catch a movie if time permits.',
    'LOW', 
    TRUE, 
    'Personal',
    'admin', CURRENT_TIMESTAMP, 'admin', CURRENT_TIMESTAMP
);
