package de.iotiq.cotwinbackend.messages.response;

import de.iotiq.cotwinbackend.domain.Note;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * DTO for Note entity responses.
 */
@Data
public class NoteDTO {
    private Long id;
    private String title;
    private String content;
    private Note.NotePriority priority;
    private Boolean completed;
    private String category;
    private LocalDateTime createdDate;
    private LocalDateTime lastModifiedDate;
    private String createdBy;
    private String lastModifiedBy;
    
    // Computed fields
    private String displayTitle;
    private String priorityIcon;
}
