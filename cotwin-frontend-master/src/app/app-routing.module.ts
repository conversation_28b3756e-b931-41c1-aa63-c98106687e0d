import { RouterModule } from "@angular/router";
import { NgModule } from "@angular/core";
import { DashboardDemoComponent } from "./demo/view/dashboarddemo.component";
import { AppMainComponent } from "./components/main/app.main.component";
import { AppNotfoundComponent } from "./pages/app.notfound.component";
import { AppErrorComponent } from "./pages/app.error.component";
import { AppAccessdeniedComponent } from "./pages/app.accessdenied.component";
import { LoginPageComponent } from "./pages/login-page/login-page.component";
import { AuthGuard } from "./guards/auth.guard";
import { ForumPageComponent } from "./pages/forum/forum-page/forum-page.component";
import { DashboardPageComponent } from "./pages/dashboard/dashboard-page/dashboard-page.component";
import { MachinePlanListPageComponent } from "./pages/machine/plan-phase/machine-plan-list-page/machine-plan-list-page.component";
import { MachineCreatePageComponent } from "./pages/machine/machine-create-page/machine-create-page.component";
import { ProductPageComponent } from "./pages/product/product-page/product-page.component";
import { MachineBuildListPageComponent } from "./pages/machine/build-phase/machine-build-list-page/machine-build-list-page.component";
import { MachineRunListPageComponent } from "./pages/machine/run-phase/machine-run-list-page/machine-run-list-page.component";
import { MachineRunDashboardPageComponent } from "./pages/machine/run-phase/machine-run-dashboard-page/machine-run-dashboard-page.component";
import { MachinePlanConfigPageComponent } from "./pages/machine/plan-phase/machine-plan-config-page/machine-plan-config-page.component";
import { MachineDetailsPageComponent } from "./pages/machine/machine-details-page/machine-details-page.component";
import { CompanyDetailsPageComponent } from "./pages/company/company-details-page/company-details-page.component";
import { UserDetailsPageComponent } from "./pages/user/user-details-page/user-details-page.component";
import { AdministrationOverviewPageComponent } from "./pages/administrator/administration-overview-page/administration-overview-page.component";
import { CompanyCreatePageComponent } from "./pages/company/company-create-page/company-create-page.component";
import { UserCreatePageComponent } from "./pages/user/user-create-page/user-create-page.component";
import { NotificationsPageComponent } from "./pages/notification/notifications-page/notifications-page.component";
import { MachineEditPageComponent } from "./pages/machine/machine-edit-page/machine-edit-page.component";
import { MachineBuildConfigPageComponent } from "./pages/machine/build-phase/machine-build-config-page/machine-build-config-page.component";
import { MachineRunOrderpartsPageComponent } from "./pages/machine/run-phase/machine-run-orderparts-page/machine-run-orderparts-page.component";
import { SearchResultsPageComponent } from "./pages/search/search-results-page/search-results-page.component";
import { ServiceRequestsPageComponent } from "./pages/machine/service-requests-page/service-requests-page.component";
import { KnowledgePageComponent } from "./pages/knowledge/knowledge-page/knowledge-page.component";
import { CotwinExplorePageComponent } from "./pages/knowledge/cotwin-explore-page/cotwin-explore-page.component";
import { CreateThreadPageComponent } from "./pages/forum/thread/create-thread-page/create-thread-page.component";
import { ThreadViewPageComponent } from "./pages/forum/thread/thread-view-page/thread-view-page.component";
import { ProfilePageComponent } from "./pages/profile/profile-page/profile-page.component";
import { ForumSearchComponent } from "./components/forum/search/forum-search.component";
import { ForumSearchPageComponent } from "./pages/forum/search/forum-search-page.component";
import { MachineRunConfigPageComponent } from "./pages/machine/run-phase/machine-run-config-page/machine-run-config-page.component";
import { CatalogPageComponent } from "./pages/catalog/catalog-page/catalog-page.component";
import { MachineAllListPageComponent } from "./pages/machine/machine-all-list-page/machine-all-list-page.component";

@NgModule({
  imports: [
    RouterModule.forRoot(
      [
        {
          path: "",
          component: AppMainComponent,
          children: [
            {
              path: DashboardPageComponent.ROUTE.substring(1),
              component: DashboardPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewDashboard",
                meta: { routeName: "home", parent: "" },
              },
            },
            {
              path: "",
              component: DashboardPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewDashboard",
                meta: { routeName: "home", parent: "" },
              },
            },
            {
              path: ForumPageComponent.ROUTE.substring(1),
              component: ForumPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewForum",
                meta: { routeName: "forum", parent: "home" },
              },
            },
            {
              path: ForumSearchPageComponent.ROUTE.substring(1),
              component: ForumSearchPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "forumSearch",
                meta: { routeName: "forumSearch", parent: "forum" },
              },
            },
            {
              path: CreateThreadPageComponent.ROUTE.substring(1),
              component: CreateThreadPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "createThread",
                meta: { routeName: "createThread", parent: "forum" },
              },
            },
            {
              path: MachinePlanListPageComponent.ROUTE.substring(1),
              component: MachinePlanListPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewPlanPhaseMachineList",
                meta: { routeName: "machinePlanList", parent: "home" },
              },
            },
            {
              path: MachineCreatePageComponent.ROUTE.substring(1),
              component: MachineCreatePageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "createMachine",
                meta: { routeName: "machineCreate", parent: "machinePlanList" },
              },
            },
            {
              path: ProductPageComponent.ROUTE.substring(1),
              component: ProductPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewProducts",
                meta: { routeName: "products", parent: "home" },
              },
            },
            {
              path: MachineBuildListPageComponent.ROUTE.substring(1),
              component: MachineBuildListPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewBuildPhaseMachineList",
                meta: { routeName: "machineBuildList", parent: "home" },
              },
            },
            {
              path: MachineRunListPageComponent.ROUTE.substring(1),
              component: MachineRunListPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewRunPhaseMachineList",
                meta: { routeName: "machineRunList", parent: "home" },
              },
            },
            {
              path: MachineRunDashboardPageComponent.ROUTE,
              component: MachineRunDashboardPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewRunPhaseDashboard",
                meta: { routeName: "dashboard", parent: "machineRunList" },
              },
            },
            {
              path: MachinePlanConfigPageComponent.ROUTE.substring(1),
              component: MachinePlanConfigPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewPlanPhaseConfig",
                meta: { routeName: "config", parent: "machineDetails" },
              },
            },
            {
              path: MachineDetailsPageComponent.ROUTE,
              component: MachineDetailsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewMachineDetails",
                meta: {
                  routeName: "machineDetails",
                  parent: "machineRedirect",
                },
              },
            },
            {
              path: CompanyDetailsPageComponent.ROUTE,
              component: CompanyDetailsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewCompanyDetails",
                meta: {
                  routeName: "companyDetails",
                  parent: "administrationOverview",
                },
              },
            },
            {
              path: UserDetailsPageComponent.ROUTE,
              component: UserDetailsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewUserDetails",
                meta: {
                  routeName: "userDetails",
                  parent: "administrationOverview",
                },
              },
            },
            {
              path: AdministrationOverviewPageComponent.ROUTE,
              component: AdministrationOverviewPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewAdministrationOverview",
                meta: { routeName: "administrationOverview", parent: "home" },
              },
            },
            {
              path: CompanyCreatePageComponent.ROUTE.substring(1),
              component: CompanyCreatePageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "createCompany",
                meta: {
                  routeName: "companyCreate",
                  parent: "administrationOverview",
                },
              },
            },
            {
              path: UserCreatePageComponent.ROUTE.substring(1),
              component: UserCreatePageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "createUser",
                meta: {
                  routeName: "userCreate",
                  parent: "administrationOverview",
                },
              },
            },
            {
              path: NotificationsPageComponent.ROUTE,
              component: NotificationsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewNotifications",
                meta: { routeName: "notifications", parent: "home" },
              },
            },
            {
              path: MachineEditPageComponent.ROUTE,
              component: MachineEditPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "editMachine",
                meta: { routeName: "machineEdit", parent: "machineDetails" },
              },
            },
            {
              path: MachineBuildConfigPageComponent.ROUTE,
              component: MachineBuildConfigPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewBuildPhaseConfig",
                meta: { routeName: "config", parent: "machineBuildList" },
              },
            },
            {
              path: MachineRunOrderpartsPageComponent.ROUTE,
              component: MachineRunOrderpartsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewRunPhaseOrderParts",
                meta: { routeName: "orderParts", parent: "machineRunList" },
              },
            },
            {
              path: SearchResultsPageComponent.ROUTE,
              component: SearchResultsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewSearchResults",
                meta: { routeName: "searchResults", parent: "home" },
              },
            },
            {
              path: ServiceRequestsPageComponent.ROUTE,
              component: ServiceRequestsPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewServiceRequests",
                meta: {
                  routeName: "serviceRequests",
                  parent: "machineRunList",
                },
              },
            },
            {
              path: KnowledgePageComponent.ROUTE,
              component: KnowledgePageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewKnowledge",
                meta: { routeName: "knowledge", parent: "home" },
              },
            },
            {
              path: CotwinExplorePageComponent.ROUTE,
              component: CotwinExplorePageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewCotwinExplorer",
                meta: { routeName: "coTwinExplorer", parent: "knowledge" },
              },
            },
            {
              path: ThreadViewPageComponent.ROUTE,
              component: ThreadViewPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewThread",
                meta: { routeName: "thread", parent: "forum" },
              },
            },
            {
              path: ProfilePageComponent.ROUTE.substring(1),
              component: ProfilePageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewProfile",
                meta: { routeName: "profile", parent: "home" },
              },
            },
            {
              path: MachineRunConfigPageComponent.ROUTE,
              component: MachineRunConfigPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewRunPhaseConfig",
                meta: { routeName: "config", parent: "machineRunList" },
              },
            },
            {
              path: CatalogPageComponent.ROUTE,
              component: CatalogPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewCatalog",
                meta: { routeName: "catalog", parent: "home" },
              },
            },
            {
              path: MachineAllListPageComponent.ROUTE,
              component: MachineAllListPageComponent,
              canActivate: [AuthGuard],
              data: {
                feature: "viewAllMachines",
                meta: { routeName: "machines", parent: "home" },
              },
            },
            {
              path: "notes",
              loadChildren: () => import('./pages/note/note.module').then(m => m.NoteModule),
              canActivate: [AuthGuard],
              data: {
                meta: { routeName: "notes", parent: "home" },
              },
            },
          ],
        },
        { path: "error", component: AppErrorComponent },
        { path: "access", component: AppAccessdeniedComponent },
        { path: "notfound", component: AppNotfoundComponent },
        {
          path: LoginPageComponent.ROUTE.substring(1),
          component: LoginPageComponent,
        },
        { path: "**", redirectTo: "/notfound" },
      ],
      { scrollPositionRestoration: "enabled", anchorScrolling: "enabled" }
    ),
  ],
  exports: [RouterModule],
})
export class AppRoutingModule {}
