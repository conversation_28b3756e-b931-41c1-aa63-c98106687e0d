# Local development configuration
spring:
  datasource:
    url: *****************************************
    username: postgres
    password: pass
  jpa:
    hibernate:
      ddl-auto: validate
    show-sql: false
    properties:
      hibernate:
        format_sql: true
        globally_quoted_identifiers: true
        cache:
          ehcache:
            missing_cache_strategy: create
          use_second_level_cache: true
          region:
            factory_class: org.hibernate.cache.EhCacheRegionFactory
        search:
          backend:
            hosts: localhost:9200
          schema_management:
            strategy: create-or-update
  flyway:
    url: *****************************************
    user: postgres
    password: pass
    validate-on-migrate: false
  kafka:
    enabled: false
  sendgrid:
    api-key: *********************************************************************

# Server configuration
server:
  port: 8080

# Application specific configuration
app:
  jwt:
    issuer: iotiq
    secret: superlongsecretyoucanteventbelievebutwasntlongenoughsthillhopefullythiswillbesufficient
    access:
      expirationMinutes: 60
    refresh:
      expirationMinutes: 262800
  email:
    senderAddress: <EMAIL>
    templateUrl: http://localhost:8080
  scheme: http
  host: localhost  # Override AAS service to localhost instead of api.co-twin.com
  port: 8080       # Use local port - AAS calls will fail gracefully

# Image storage configuration (local directory)
image:
  path: /tmp/cotwin-images
  endpoint_path: http://localhost:8080/api/image

# Admin seed configuration
seed:
  admin:
    login: admin
    pass: admin

# Swagger/OpenAPI configuration
springdoc:
  swagger-ui:
    path: /api/swagger-ui.html
  api-docs:
    path: /api-docs

# Logging configuration for development
logging:
  level:
    de.iotiq.cotwinbackend: DEBUG
    org.springframework.web: DEBUG
    org.hibernate.SQL: DEBUG
    org.hibernate.type.descriptor.sql.BasicBinder: TRACE
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"

# AAS configuration - disabled for local development
aas:
  enabled: false  # Disable AAS integration for local development
  username: iotiq
  password: gu2ftWd2wEVT9jmK
