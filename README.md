# CoTwin - Digital Twin Platform

CoTwin is a comprehensive digital twin platform designed for industrial automation and manufacturing. It provides a complete solution for managing machines, companies, users, and collaborative features through a modern web-based interface.

## 🏗️ Architecture Overview

CoTwin follows a modern microservices architecture with the following components:

- **Backend**: Spring Boot application with REST APIs
- **Frontend**: Angular-based single-page application
- **Database**: PostgreSQL with Flyway migrations
- **Search**: Elasticsearch integration via Hibernate Search
- **Message Queue**: Kafka for event-driven communication
- **Containerization**: Docker and Docker Compose support

## 📋 Features

### Core Functionality
- **Machine Management**: Complete lifecycle management of industrial machines
- **Company Management**: Organization and company information handling
- **User Management**: Authentication, authorization, and user profiles
- **Forum System**: Collaborative discussion threads and posts
- **Tag System**: Flexible categorization and labeling
- **Search & Filter**: Advanced full-text search across all entities
- **Person Management**: Contact and team member management (New Feature)

### Technical Features
- RESTful API design with OpenAPI/Swagger documentation
- Full-text search with Elasticsearch
- Real-time updates with WebSocket support
- Comprehensive audit logging
- Multi-language support (i18n)
- Responsive web design
- Role-based access control

## 🚀 Quick Start

### Prerequisites

- Java 17 or higher
- Node.js 16+ and npm
- PostgreSQL 11+
- Docker and Docker Compose (optional)

### Development Setup

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd cotwin
   ```

2. **Backend Setup**
   ```bash
   cd cotwin-backend-master
   
   # Configure database connection in application-local.yml
   # Start PostgreSQL database
   
   # Run the application
   ./gradlew bootRun --args='--spring.profiles.active=local'
   ```

3. **Frontend Setup**
   ```bash
   cd cotwin-frontend-master
   
   # Install dependencies
   npm install
   
   # Start development server
   npm start
   ```

4. **Access the application**
   - Frontend: http://localhost:4200
   - Backend API: http://localhost:8080
   - API Documentation: http://localhost:8080/swagger-ui.html

### Docker Setup

```bash
# Start all services with Docker Compose
cd cotwin-backend-master
docker-compose up -d

# The application will be available at:
# - Frontend: http://localhost:4200
# - Backend: http://localhost:8080
```

## 📚 Documentation

### API Documentation
- **Swagger UI**: Available at `/swagger-ui.html` when running the backend
- **OpenAPI Spec**: Available at `/v3/api-docs`

### Database Schema
- **Migrations**: Located in `src/main/resources/db/migration/`
- **Entity Relationships**: See [Database Schema Documentation](docs/database-schema.md)

### Frontend Architecture
- **Components**: Modular Angular components in `src/app/pages/`
- **Services**: API services in `src/app/api/services/`
- **Routing**: Configured in `app-routing.module.ts`

## 🔧 Configuration

### Backend Configuration

Key configuration files:
- `application.yml` - Base configuration
- `application-local.yml` - Local development settings
- `application-prod.yml` - Production settings

Important settings:
```yaml
# Database
spring:
  datasource:
    url: *****************************************
    username: postgres
    password: postgres

# Security
jwt:
  secret: your-secret-key
  expiration: 86400

# AAS Integration (can be disabled for local development)
aas:
  enabled: false
```

### Frontend Configuration

Configuration files:
- `src/environments/environment.ts` - Development environment
- `src/environments/environment.prod.ts` - Production environment

## 🧪 Testing

### Backend Testing
```bash
cd cotwin-backend-master
./gradlew test
```

### Frontend Testing
```bash
cd cotwin-frontend-master
npm test
npm run e2e
```

## 🚀 Deployment

### Production Deployment

1. **Build the applications**
   ```bash
   # Backend
   cd cotwin-backend-master
   ./gradlew build
   
   # Frontend
   cd cotwin-frontend-master
   npm run build:prod
   ```

2. **Deploy with Docker**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Environment Variables

Key environment variables for production:
- `SPRING_PROFILES_ACTIVE=prod`
- `DATABASE_URL`
- `JWT_SECRET`
- `ELASTICSEARCH_URL`
- `KAFKA_BOOTSTRAP_SERVERS`

## 🔐 Security

- JWT-based authentication
- Role-based access control (ADMIN, MANUFACTURER, USER)
- CORS configuration for cross-origin requests
- Input validation and sanitization
- SQL injection prevention with JPA

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines

- Follow Java coding standards and Spring Boot best practices
- Use Angular style guide for frontend development
- Write unit tests for new features
- Update documentation for API changes
- Use meaningful commit messages

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the [documentation](docs/)
- Review the API documentation at `/swagger-ui.html`

## 🔄 Version History

- **v1.0.0** - Initial release with core functionality
- **v1.1.0** - Added person management feature
- **v1.2.0** - Enhanced search capabilities
- **v1.3.0** - Improved UI/UX and performance optimizations

---

**CoTwin** - Empowering Digital Transformation in Manufacturing
