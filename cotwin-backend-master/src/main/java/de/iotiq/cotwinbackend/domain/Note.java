package de.iotiq.cotwinbackend.domain;

import lombok.Getter;
import lombok.RequiredArgsConstructor;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.search.mapper.pojo.mapping.definition.annotation.FullTextField;
import org.hibernate.search.mapper.pojo.mapping.definition.annotation.Indexed;

import javax.persistence.*;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.io.Serial;
import java.io.Serializable;
import java.util.Objects;

/**
 * A simple Note entity for demonstration of platform extensibility.
 * This is a standalone feature that doesn't depend on other entities.
 */
@Entity
@Table(name = "note")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Getter
@Setter
@ToString
@RequiredArgsConstructor
@Indexed
public class Note extends AbstractAuditingEntity implements Serializable {

    @Serial
    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "note_seq_gen")
    @SequenceGenerator(name = "note_seq_gen", allocationSize = 1)
    private Long id;

    @NotNull
    @Size(min = 1, max = 100)
    @Column(name = "title", length = 100, nullable = false)
    @FullTextField
    private String title;

    @Size(max = 1000)
    @Column(name = "content", length = 1000)
    @FullTextField
    private String content;

    @Enumerated(EnumType.STRING)
    @Column(name = "priority")
    private NotePriority priority = NotePriority.MEDIUM;

    @Column(name = "completed")
    private Boolean completed = false;

    @Size(max = 50)
    @Column(name = "category", length = 50)
    @FullTextField
    private String category;

    // Utility methods
    public String getDisplayTitle() {
        return title + (completed ? " ✓" : "");
    }

    public String getPriorityIcon() {
        return switch (priority) {
            case HIGH -> "🔴";
            case MEDIUM -> "🟡";
            case LOW -> "🟢";
        };
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        Note note = (Note) o;
        return Objects.equals(id, note.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(id);
    }

    /**
     * Note priority enumeration
     */
    public enum NotePriority {
        HIGH, MEDIUM, LOW
    }
}
