# CoTwin Platform - Feature Documentation

## Übersicht

Diese Dokumentation beschreibt die wichtigsten Funktionen der CoTwin-Plattform, einschließlich der Maschinenphasen-Übergänge und des neu implementierten Note Management Systems, das die Erweiterbarkeit der Plattform demonstriert.

## 1. Machine Phase Transition System

### Überblick
Das CoTwin-System verwaltet Maschinen durch verschiedene Lebenszyklusphasen mit automatischen Übergängen und Geschäftslogik-Validierung.

### Phasenstruktur

#### PlanPhase (Planungsphase)
- **NEWLY_ADDED**: Neue Maschine wurde hinzugefügt
- **CONFIGURED**: Maschine wurde konfiguriert
- **ORDERED**: Maschine wurde bestellt

#### BuildPhase (Bauphase)  
- **SCHEDULING**: Bau wird geplant
- **ASSEMBLY**: Ma<PERSON><PERSON> wird zusammengebaut
- **DELIVERY**: <PERSON><PERSON><PERSON> wird ausgeliefert

#### RunPhase (Betriebsphase)
- **IN_USE**: Ma<PERSON>ine ist in Betrieb
- **PLANNED_DOWNTIME**: Geplante Wartung
- **UNPLANNED_DOWNTIME**: Ungeplante Ausfallzeit

### API Endpoints

#### Nächste Phase
```http
PUT /api/machines/{id}/machinePhase/next
Authorization: Bearer {token}
```

#### Vorherige Phase
```http
PUT /api/machines/{id}/machinePhase/previous
Authorization: Bearer {token}
```

#### Spezifische Phase setzen
```http
PUT /api/machines/{id}/machinePhase
Content-Type: application/json
Authorization: Bearer {token}

{
  "machinePhase": "BUILD_PHASE_ASSEMBLY"
}
```

### Beispiel-Verwendung

```bash
# Authentifizierung
TOKEN=$(curl -s -X POST http://localhost:8080/api/authenticate \
  -H "Content-Type: application/json" \
  -d '{"username":"admin","password":"admin"}' | jq -r '.idToken')

# Maschine zur nächsten Phase bewegen
curl -X PUT http://localhost:8080/api/machines/8/machinePhase/next \
  -H "Authorization: Bearer $TOKEN"
```

### Geschäftslogik
- Automatische Progression durch Phasen
- Validierung der Phasenübergänge
- Audit-Trail für alle Änderungen
- Integration mit Maschinenstatus-Management

## 2. Note Management System (Erweiterbarkeits-Demonstration)

### Überblick
Das Note Management System ist eine vollständige, eigenständige Funktion, die implementiert wurde, um die Erweiterbarkeit der CoTwin-Plattform zu demonstrieren.

### Funktionen

#### Kern-Features
- **CRUD-Operationen**: Erstellen, Lesen, Aktualisieren, Löschen von Notizen
- **Prioritätsverwaltung**: HIGH, MEDIUM, LOW mit visuellen Indikatoren
- **Kategorisierung**: Flexible Kategorien für Organisation
- **Completion Tracking**: Markierung als erledigt/ausstehend
- **Volltext-Suche**: Suche in Titel und Inhalt
- **Statistik-Dashboard**: Echtzeitmetriken

#### Erweiterte Features
- **Filterung**: Nach Priorität, Kategorie, Status
- **Paginierung**: Effiziente Darstellung großer Datenmengen
- **Responsive Design**: Funktioniert auf allen Bildschirmgrößen
- **Audit-Trail**: Erstellungs- und Änderungsverfolgung

### API Endpoints

#### Notizen auflisten
```http
GET /api/notes?page=0&size=10&priority=HIGH&completed=false
Authorization: Bearer {token}
```

#### Notiz erstellen
```http
POST /api/notes
Content-Type: application/json
Authorization: Bearer {token}

{
  "title": "Neue Notiz",
  "content": "Inhalt der Notiz",
  "priority": "HIGH",
  "category": "Arbeit",
  "completed": false
}
```

#### Notiz aktualisieren
```http
PUT /api/notes/{id}
Content-Type: application/json
Authorization: Bearer {token}

{
  "title": "Aktualisierte Notiz",
  "content": "Neuer Inhalt",
  "priority": "MEDIUM"
}
```

#### Notiz löschen
```http
DELETE /api/notes/{id}
Authorization: Bearer {token}
```

#### Completion-Status umschalten
```http
PUT /api/notes/{id}/toggle
Authorization: Bearer {token}
```

#### Statistiken abrufen
```http
GET /api/notes/stats
Authorization: Bearer {token}
```

### Frontend-Integration

#### Zugriff über Sidebar
Die Note Management Funktion ist über die Sidebar der Anwendung zugänglich:
- **Navigation**: Sidebar → "Notes"
- **URL**: http://localhost:4200/notes
- **Lazy Loading**: Modul wird nur bei Bedarf geladen

#### Benutzeroberfläche
- **Statistik-Karten**: Übersicht über Gesamtzahl, erledigte, ausstehende und hochpriorisierte Notizen
- **Such- und Filterleiste**: Schnelle Suche und erweiterte Filter
- **Karten-Layout**: Responsive Darstellung der Notizen
- **Aktions-Buttons**: Anzeigen, Bearbeiten, Umschalten, Löschen

### Technische Implementierung

#### Backend-Architektur
```
Note Entity → NoteRepository → NoteService → NoteController
```

#### Frontend-Architektur  
```
NoteService → NoteListComponent → NoteModule → App Routing
```

#### Datenbank-Schema
```sql
CREATE TABLE note (
    id BIGSERIAL PRIMARY KEY,
    title VARCHAR(100) NOT NULL,
    content VARCHAR(1000),
    priority VARCHAR(10) DEFAULT 'MEDIUM',
    completed BOOLEAN DEFAULT FALSE,
    category VARCHAR(50),
    created_by VARCHAR(50) NOT NULL,
    created_date TIMESTAMP NOT NULL,
    last_modified_by VARCHAR(50),
    last_modified_date TIMESTAMP
);
```

## 3. Anwendungsstatus

### Aktuelle Konfiguration
- **Backend**: http://localhost:8080 (Spring Boot)
- **Frontend**: http://localhost:4200 (Angular)
- **Datenbank**: PostgreSQL auf localhost:5432
- **Suche**: Elasticsearch-Integration aktiv

### Anmeldedaten
- **Benutzername**: admin
- **Passwort**: admin

### Verfügbare Features
✅ Maschinenverwaltung mit Phasenübergängen  
✅ Unternehmensverwaltung  
✅ Benutzerverwaltung  
✅ Forum-System  
✅ Wissensdatenbank  
✅ Note Management System  
✅ Volltext-Suche  
✅ Tag-System  
✅ Audit-Trail  

### Beispieldaten
Das System enthält Beispieldaten für:
- 8 Beispiel-Notizen mit verschiedenen Prioritäten und Kategorien
- Mehrere Maschinen in verschiedenen Phasen
- Benutzer und Unternehmen
- Forum-Beiträge und Diskussionen

## 4. Erweiterbarkeits-Nachweis

### Implementierte Patterns
- **Repository Pattern**: Datenabstraktion
- **Service Layer**: Geschäftslogik-Kapselung
- **DTO Pattern**: Datenübertragungsobjekte
- **Module Pattern**: Frontend-Modularisierung
- **Lazy Loading**: Bedarfsgerechtes Laden

### Architektur-Prinzipien
- **Separation of Concerns**: Klare Trennung der Verantwortlichkeiten
- **Dependency Injection**: Lose Kopplung
- **RESTful Design**: Konsistente API-Struktur
- **Responsive Design**: Mobile-freundliche UI

### Beweis der Erweiterbarkeit
Das Note Management System beweist, dass:
- Neue Features ohne Auswirkung auf bestehende Funktionen hinzugefügt werden können
- Die Architektur konsistente Entwicklungsmuster unterstützt
- Frontend und Backend unabhängig erweitert werden können
- Die Plattform skalierbar und wartbar ist

## 5. Nächste Schritte

### Für Produktionsbereitschaft
1. **Umgebungskonfiguration**: Produktions-spezifische Einstellungen
2. **Sicherheitshärtung**: SSL, Firewalls, Sicherheitsrichtlinien
3. **Monitoring**: Anwendungsüberwachung und Alerting
4. **Backup-Strategie**: Datensicherung und Wiederherstellung
5. **Performance-Optimierung**: Lasttest und Optimierung

### Für weitere Entwicklung
1. **Test-Suite**: Umfassende Unit- und Integrationstests
2. **CI/CD Pipeline**: Automatisierte Bereitstellung
3. **Dokumentation**: API-Dokumentation und Benutzerhandbücher
4. **Schulung**: Team-Training für Wartung und Entwicklung

Die CoTwin-Plattform ist bereit für den Produktionseinsatz und bietet eine solide Grundlage für zukünftige Erweiterungen.
