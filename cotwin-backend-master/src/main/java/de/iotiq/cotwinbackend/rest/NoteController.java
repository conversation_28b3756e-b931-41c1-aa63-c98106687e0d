package de.iotiq.cotwinbackend.rest;

import de.iotiq.cotwinbackend.messages.request.GetNoteRequest;
import de.iotiq.cotwinbackend.messages.request.SaveNoteRequest;
import de.iotiq.cotwinbackend.messages.response.NoteDTO;
import de.iotiq.cotwinbackend.security.AuthoritiesConstants;
import de.iotiq.cotwinbackend.service.NoteService;
import de.iotiq.cotwinbackend.util.PaginationUtil;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;

import javax.validation.Valid;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;

/**
 * REST controller for managing {@link de.iotiq.cotwinbackend.domain.Note}.
 * This is a simple standalone feature demonstrating platform extensibility.
 */
@RestController
@RequestMapping("/api/notes")
@RequiredArgsConstructor
@Slf4j
@Tag(name = "Note Management", description = "Simple note-taking system for demonstration")
@PreAuthorize("hasAnyAuthority(\"" + AuthoritiesConstants.ADMIN + "\",\"" + AuthoritiesConstants.MANUFACTURER + "\",\"" + AuthoritiesConstants.END_CUSTOMER + "\")")
public class NoteController {

    private final NoteService noteService;

    /**
     * {@code POST  /notes} : Create a new note.
     */
    @PostMapping
    @Operation(summary = "Create a new note", description = "Creates a new note with the provided information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "201", description = "Note created successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data")
    })
    public ResponseEntity<NoteDTO> createNote(@Valid @RequestBody SaveNoteRequest request) 
            throws URISyntaxException {
        log.debug("REST request to save Note : {}", request);
        
        NoteDTO result = noteService.createNote(request);
        return ResponseEntity.created(new URI("/api/notes/" + result.getId()))
            .body(result);
    }

    /**
     * {@code PUT  /notes/:id} : Updates an existing note.
     */
    @PutMapping("/{id}")
    @Operation(summary = "Update an existing note", description = "Updates an existing note with the provided information")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Note updated successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid input data"),
        @ApiResponse(responseCode = "404", description = "Note not found")
    })
    public ResponseEntity<NoteDTO> updateNote(
            @Parameter(description = "Note ID") @PathVariable(value = "id", required = false) final Long id,
            @Valid @RequestBody SaveNoteRequest request) {
        log.debug("REST request to update Note : {}, {}", id, request);
        
        NoteDTO result = noteService.updateNote(id, request);
        return ResponseEntity.ok(result);
    }

    /**
     * {@code GET  /notes} : get all notes with filtering.
     */
    @GetMapping
    @Operation(summary = "Get all notes", description = "Retrieves a paginated list of notes with optional filtering")
    @ApiResponse(responseCode = "200", description = "Notes retrieved successfully")
    public ResponseEntity<List<NoteDTO>> searchNotes(
            GetNoteRequest request, 
            Pageable pageable) {
        log.debug("REST request to search Notes : {}", request);
        
        Page<NoteDTO> page = noteService.searchNotes(request, pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(
            ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return new ResponseEntity<>(page.getContent(), headers, HttpStatus.OK);
    }

    /**
     * {@code GET  /notes/:id} : get the "id" note.
     */
    @GetMapping("/{id}")
    @Operation(summary = "Get note by ID", description = "Retrieves a specific note by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Note found"),
        @ApiResponse(responseCode = "404", description = "Note not found")
    })
    public ResponseEntity<NoteDTO> getNote(
            @Parameter(description = "Note ID") @PathVariable Long id) {
        log.debug("REST request to get Note : {}", id);
        
        return ResponseEntity.of(noteService.findNote(id));
    }

    /**
     * {@code DELETE  /notes/:id} : delete the "id" note.
     */
    @DeleteMapping("/{id}")
    @Operation(summary = "Delete note", description = "Deletes a note by its ID")
    @ApiResponses(value = {
        @ApiResponse(responseCode = "204", description = "Note deleted successfully"),
        @ApiResponse(responseCode = "404", description = "Note not found")
    })
    public ResponseEntity<Void> deleteNote(
            @Parameter(description = "Note ID") @PathVariable Long id) {
        log.debug("REST request to delete Note : {}", id);
        
        noteService.deleteNote(id);
        return ResponseEntity.noContent().build();
    }

    /**
     * {@code PUT  /notes/:id/toggle} : toggle completion status of the note.
     */
    @PutMapping("/{id}/toggle")
    @Operation(summary = "Toggle note completion", description = "Toggles the completion status of a note")
    @ApiResponse(responseCode = "200", description = "Note completion status toggled successfully")
    public ResponseEntity<NoteDTO> toggleNoteCompletion(
            @Parameter(description = "Note ID") @PathVariable Long id) {
        log.debug("REST request to toggle completion for Note : {}", id);
        
        NoteDTO result = noteService.toggleNoteCompletion(id);
        return ResponseEntity.ok(result);
    }

    /**
     * {@code GET  /notes/stats} : get note statistics.
     */
    @GetMapping("/stats")
    @Operation(summary = "Get note statistics", description = "Retrieves statistics about notes")
    @ApiResponse(responseCode = "200", description = "Statistics retrieved successfully")
    public ResponseEntity<NoteService.NoteStatsDTO> getNoteStatistics() {
        log.debug("REST request to get Note statistics");
        
        NoteService.NoteStatsDTO stats = noteService.getNoteStatistics();
        return ResponseEntity.ok(stats);
    }
}
