import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { RouterModule } from '@angular/router';

import { NoteListComponent } from './note-list/note-list.component';
import { NoteService } from '../../api/services/note.service';

@NgModule({
  declarations: [
    NoteListComponent
  ],
  imports: [
    CommonModule,
    FormsModule,
    ReactiveFormsModule,
    RouterModule.forChild([
      {
        path: '',
        component: NoteListComponent
      }
    ])
  ]
})
export class NoteModule { }
