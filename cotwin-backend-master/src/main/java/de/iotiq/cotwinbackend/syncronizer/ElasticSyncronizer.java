package de.iotiq.cotwinbackend.syncronizer;

import de.iotiq.cotwinbackend.domain.*;
import de.iotiq.cotwinbackend.domain.Thread;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.hibernate.search.mapper.orm.Search;
import org.hibernate.search.mapper.orm.massindexing.MassIndexer;
import org.hibernate.search.mapper.orm.session.SearchSession;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.persistence.EntityManager;

@Component
@RequiredArgsConstructor
@Slf4j
public class ElasticSyncronizer {
    private final EntityManager entityManager;

    @Transactional
    public void sync() {
        syncExisting(Machine.class);
        syncExisting(Tag.class);
        syncExisting(Company.class);
        syncExisting(Thread.class);
        syncExisting(Post.class);
        syncExisting(User.class);
        syncExisting(Note.class);
    }

    void syncExisting(Class<?> clazz) {
        try {
            SearchSession searchSession = Search.session(entityManager);
            MassIndexer indexer = searchSession.massIndexer(clazz)
                    .threadsToLoadObjects(7);
            indexer.startAndWait();
        } catch (Exception ex) {
            log.error(String.format("syncExisting Error %s", clazz.getName()), ex);
        }
    }
}
