package de.iotiq.cotwinbackend.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import de.iotiq.cotwinbackend.domain.converter.LongTypeBridge;
import lombok.Getter;
import lombok.Setter;
import lombok.ToString;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.search.engine.backend.types.Projectable;
import org.hibernate.search.engine.backend.types.Sortable;
import org.hibernate.search.mapper.pojo.bridge.mapping.annotation.ValueBridgeRef;
import org.hibernate.search.mapper.pojo.mapping.definition.annotation.*;

import javax.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;

@Entity
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@Getter
@Setter
@Indexed
public class Company implements Serializable {

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "company_gen")
    @SequenceGenerator(name = "company_gen", allocationSize = 1)
    @KeywordField(valueBridge = @ValueBridgeRef(type = LongTypeBridge.class))
    @GenericField(name = "id_sort",projectable = Projectable.YES, sortable = Sortable.YES)
    private Long id;
    @FullTextField
    @GenericField(name = "name_sort",projectable = Projectable.YES, sortable = Sortable.YES)
    private String name;

    @Embedded
    @IndexedEmbedded
    private Address address;

    @Embedded
    private Person contactPerson;

    @OneToMany(mappedBy = "company")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = {"responsibleUser", "template", "tags", "company", "serviceRequests"}, allowSetters = true)
    private Set<Machine> machines = new HashSet<>();

    @OneToMany(mappedBy = "targetCompany")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = {"responsibleUser", "template", "tags", "company", "serviceRequests"}, allowSetters = true)
    private Set<Machine> targetMachines = new HashSet<>();

    @OneToMany(mappedBy = "company")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = {"authorities"}, allowSetters = true)
    @ToString.Exclude
    private Set<User> users = new HashSet<>();
}
