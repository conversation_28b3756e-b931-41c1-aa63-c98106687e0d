<div class="note-list-container">
  <!-- Header -->
  <div class="d-flex justify-content-between align-items-center mb-4">
    <h2>📝 Note Management</h2>
    <button class="btn btn-primary" (click)="createNote()">
      <i class="pi pi-plus"></i> Add Note
    </button>
  </div>

  <!-- Statistics Cards -->
  <div class="row mb-4" *ngIf="stats">
    <div class="col-md-3">
      <div class="card stats-card">
        <div class="card-body text-center">
          <h3 class="text-primary">{{ stats.totalNotes }}</h3>
          <p class="mb-0">Total Notes</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card stats-card">
        <div class="card-body text-center">
          <h3 class="text-success">{{ stats.completedNotes }}</h3>
          <p class="mb-0">Completed</p>
          <small class="text-muted">{{ getCompletionPercentage() }}%</small>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card stats-card">
        <div class="card-body text-center">
          <h3 class="text-warning">{{ stats.pendingNotes }}</h3>
          <p class="mb-0">Pending</p>
        </div>
      </div>
    </div>
    <div class="col-md-3">
      <div class="card stats-card">
        <div class="card-body text-center">
          <h3 class="text-danger">{{ stats.highPriorityNotes }}</h3>
          <p class="mb-0">High Priority</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Search and Filter Controls -->
  <div class="card mb-4">
    <div class="card-body">
      <!-- Search Bar -->
      <div class="row mb-3">
        <div class="col-md-8">
          <div class="input-group">
            <input 
              type="text" 
              class="form-control" 
              placeholder="Search notes by title or content..."
              [(ngModel)]="searchQuery"
              (keyup.enter)="onSearch()"
            >
            <div class="input-group-append">
              <button class="btn btn-outline-secondary" type="button" (click)="onSearch()">
                <i class="pi pi-search"></i>
              </button>
              <button class="btn btn-outline-secondary" type="button" (click)="onSearchClear()" *ngIf="searchQuery">
                <i class="pi pi-times"></i>
              </button>
            </div>
          </div>
        </div>
        <div class="col-md-4 text-right">
          <button class="btn btn-outline-primary" (click)="toggleFilters()">
            <i class="pi pi-filter"></i> 
            {{ showFilters ? 'Hide Filters' : 'Show Filters' }}
          </button>
        </div>
      </div>

      <!-- Advanced Filters -->
      <div *ngIf="showFilters" class="border-top pt-3">
        <form [formGroup]="filterForm">
          <div class="row">
            <div class="col-md-3">
              <label for="title">Title</label>
              <input type="text" id="title" class="form-control" formControlName="title" placeholder="Note title">
            </div>
            <div class="col-md-3">
              <label for="category">Category</label>
              <input type="text" id="category" class="form-control" formControlName="category" placeholder="Category">
            </div>
            <div class="col-md-3">
              <label for="priority">Priority</label>
              <select id="priority" class="form-control" formControlName="priority">
                <option value="">All Priorities</option>
                <option *ngFor="let priority of priorities" [value]="priority">{{ priority }}</option>
              </select>
            </div>
            <div class="col-md-3">
              <label for="completed">Status</label>
              <select id="completed" class="form-control" formControlName="completed">
                <option value="">All</option>
                <option [value]="true">Completed</option>
                <option [value]="false">Pending</option>
              </select>
            </div>
          </div>
          <div class="row mt-3">
            <div class="col-12 text-right">
              <button type="button" class="btn btn-secondary" (click)="clearFilters()">Clear Filters</button>
            </div>
          </div>
        </form>
      </div>
    </div>
  </div>

  <!-- Results -->
  <div class="card">
    <div class="card-body">
      <!-- Loading indicator -->
      <div *ngIf="loading" class="text-center py-4">
        <div class="spinner-border" role="status">
          <span class="sr-only">Loading...</span>
        </div>
      </div>

      <!-- Results table -->
      <div *ngIf="!loading">
        <div class="d-flex justify-content-between align-items-center mb-3">
          <span class="text-muted">{{ totalElements }} note(s) found</span>
          <div class="d-flex align-items-center">
            <label class="mr-2">Show:</label>
            <select class="form-control form-control-sm" style="width: auto;" 
                    [value]="pageSize" (change)="onPageSizeChange(+$event.target.value)">
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
            </select>
          </div>
        </div>

        <!-- Notes Grid -->
        <div class="row">
          <div class="col-md-6 col-lg-4 mb-3" *ngFor="let note of notes">
            <div class="card note-card h-100" [class.completed]="note.completed">
              <div class="card-header d-flex justify-content-between align-items-start">
                <div class="flex-grow-1">
                  <h6 class="card-title mb-1" [class.text-muted]="note.completed">
                    {{ note.title }}
                    <span *ngIf="note.completed" class="ml-1">✓</span>
                  </h6>
                  <div class="d-flex align-items-center">
                    <span class="badge mr-2" [ngClass]="getPriorityClass(note.priority)">
                      {{ note.priorityIcon }} {{ note.priority }}
                    </span>
                    <span class="badge" [ngClass]="getCompletionClass(note.completed)">
                      {{ getCompletionText(note.completed) }}
                    </span>
                  </div>
                </div>
              </div>
              <div class="card-body">
                <p class="card-text" [class.text-muted]="note.completed">
                  {{ note.content || 'No content' | slice:0:100 }}
                  <span *ngIf="note.content && note.content.length > 100">...</span>
                </p>
                <div class="note-meta">
                  <small class="text-muted">
                    <span *ngIf="note.category" class="badge badge-light mr-2">{{ note.category }}</span>
                    Created: {{ note.createdDate | date:'short' }}
                  </small>
                </div>
              </div>
              <div class="card-footer">
                <div class="btn-group btn-group-sm w-100" role="group">
                  <button class="btn btn-outline-primary" (click)="viewNote(note)" title="View">
                    <i class="pi pi-eye"></i>
                  </button>
                  <button class="btn btn-outline-secondary" (click)="editNote(note)" title="Edit">
                    <i class="pi pi-pencil"></i>
                  </button>
                  <button class="btn" 
                          [class.btn-outline-success]="!note.completed"
                          [class.btn-outline-warning]="note.completed"
                          (click)="toggleCompletion(note)" 
                          [title]="note.completed ? 'Mark as Pending' : 'Mark as Completed'">
                    <i class="pi" [class.pi-check]="!note.completed" [class.pi-times]="note.completed"></i>
                  </button>
                  <button class="btn btn-outline-danger" (click)="deleteNote(note)" title="Delete">
                    <i class="pi pi-trash"></i>
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Empty State -->
        <div *ngIf="notes.length === 0" class="text-center py-5">
          <div class="empty-state">
            <i class="pi pi-file-o" style="font-size: 4rem; color: #ccc;"></i>
            <h4 class="mt-3 text-muted">No notes found</h4>
            <p class="text-muted">
              <span *ngIf="searchQuery || showFilters">Try adjusting your search or filters.</span>
              <span *ngIf="!searchQuery && !showFilters">Create your first note to get started!</span>
            </p>
            <button class="btn btn-primary" (click)="createNote()" *ngIf="!searchQuery && !showFilters">
              <i class="pi pi-plus"></i> Create First Note
            </button>
          </div>
        </div>

        <!-- Pagination -->
        <nav *ngIf="totalElements > pageSize" aria-label="Note pagination" class="mt-4">
          <ul class="pagination justify-content-center">
            <li class="page-item" [class.disabled]="currentPage === 0">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage - 1)">Previous</a>
            </li>
            <li class="page-item" 
                *ngFor="let page of [].constructor(Math.ceil(totalElements / pageSize)); let i = index"
                [class.active]="i === currentPage">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(i)">{{ i + 1 }}</a>
            </li>
            <li class="page-item" [class.disabled]="currentPage >= Math.ceil(totalElements / pageSize) - 1">
              <a class="page-link" href="javascript:void(0)" (click)="onPageChange(currentPage + 1)">Next</a>
            </li>
          </ul>
        </nav>
      </div>
    </div>
  </div>
</div>
