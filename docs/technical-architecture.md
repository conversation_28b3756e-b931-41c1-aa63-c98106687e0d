# CoTwin Technical Architecture

## System Overview

CoTwin is built using a modern, scalable architecture that separates concerns between presentation, business logic, and data persistence layers.

## Architecture Diagram

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Angular SPA   │    │  Spring Boot    │    │   PostgreSQL    │
│   (Frontend)    │◄──►│   (Backend)     │◄──►│   (Database)    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         │              │  Elasticsearch  │              │
         │              │    (Search)     │              │
         │              └─────────────────┘              │
         │                       │                       │
         │                       ▼                       │
         │              ┌─────────────────┐              │
         └──────────────►│     Kafka       │◄─────────────┘
                        │  (Messaging)    │
                        └─────────────────┘
```

## Backend Architecture

### Core Components

#### 1. Domain Layer
- **Entities**: JPA entities representing business objects
- **Repositories**: Data access layer using Spring Data JPA
- **Value Objects**: Immutable objects for complex data types

Key Entities:
- `Machine`: Core business entity for industrial machines
- `Company`: Organization management
- `User`: Authentication and user management
- `Thread/Post`: Forum system for collaboration
- `Tag`: Flexible categorization system
- `Person`: Contact and team member management

#### 2. Service Layer
- **Business Logic**: Core business rules and operations
- **Transaction Management**: Declarative transaction handling
- **Event Publishing**: Domain events for loose coupling

Service Categories:
- **Core Services**: `MachineService`, `CompanyService`, `UserService`
- **Search Services**: Elasticsearch integration services
- **Security Services**: Authentication and authorization
- **Integration Services**: External system integrations

#### 3. Web Layer
- **REST Controllers**: RESTful API endpoints
- **Request/Response DTOs**: Data transfer objects
- **Exception Handling**: Global exception handling
- **Security Configuration**: JWT and role-based security

#### 4. Infrastructure Layer
- **Configuration**: Application configuration management
- **Database Migrations**: Flyway-based schema management
- **Caching**: Hibernate second-level cache with EhCache
- **Monitoring**: Actuator endpoints for health checks

### Design Patterns Used

1. **Repository Pattern**: Data access abstraction
2. **Service Layer Pattern**: Business logic encapsulation
3. **DTO Pattern**: Data transfer between layers
4. **Factory Pattern**: Object creation
5. **Observer Pattern**: Event-driven architecture
6. **Strategy Pattern**: Pluggable algorithms

### Security Architecture

```
┌─────────────────┐
│   JWT Token     │
│   Validation    │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│  Spring         │
│  Security       │
│  Filter Chain   │
└─────────────────┘
         │
         ▼
┌─────────────────┐
│  Role-Based     │
│  Authorization  │
└─────────────────┘
```

Security Features:
- JWT-based stateless authentication
- Role-based access control (RBAC)
- Method-level security annotations
- CORS configuration
- Input validation and sanitization

## Frontend Architecture

### Angular Application Structure

```
src/
├── app/
│   ├── api/                 # API services and models
│   ├── components/          # Reusable UI components
│   ├── pages/              # Feature modules and pages
│   ├── guards/             # Route guards
│   ├── interceptors/       # HTTP interceptors
│   ├── models/             # TypeScript interfaces
│   └── services/           # Application services
├── assets/                 # Static assets
└── environments/           # Environment configurations
```

### Key Frontend Components

1. **Core Components**
   - `AppMainComponent`: Main layout and navigation
   - `AppSideBarComponent`: Navigation sidebar
   - `AppTopBarComponent`: Top navigation bar

2. **Feature Components**
   - Machine management components
   - Company management components
   - Forum components (threads, posts)
   - User management components
   - Person management components

3. **Shared Components**
   - Form components
   - Table components
   - Modal dialogs
   - Loading indicators

### State Management

- **Services**: Singleton services for state management
- **RxJS**: Reactive programming with observables
- **Local Storage**: Client-side data persistence
- **HTTP Interceptors**: Request/response transformation

## Database Design

### Entity Relationship Overview

```
Company ──┐
          │
          ├── Machine ──── Tag
          │     │
          │     └── ServiceRequest
          │
          └── User
               │
               └── Thread ──── Post ──── Vote
                    │
                    └── Tag

Person ──── Company
     │
     └── Tag
```

### Key Relationships

1. **Company → Machine**: One-to-Many
2. **Company → User**: One-to-Many
3. **Machine → Tag**: Many-to-Many
4. **Thread → Post**: One-to-Many
5. **User → Thread**: One-to-Many (creator)
6. **Person → Company**: Many-to-One
7. **Person → Tag**: Many-to-Many

### Database Features

- **Audit Trails**: Automatic tracking of created/modified dates and users
- **Soft Deletes**: Logical deletion for important entities
- **Indexing**: Optimized indexes for search performance
- **Constraints**: Foreign key and check constraints for data integrity

## Search Architecture

### Hibernate Search Integration

```
JPA Entity ──► Hibernate Search ──► Elasticsearch ──► Search Results
     │                                      │
     └── Annotations ──────────────────────┘
```

Search Features:
- **Full-text Search**: Across all searchable entities
- **Faceted Search**: Category-based filtering
- **Autocomplete**: Real-time search suggestions
- **Highlighting**: Search term highlighting in results

### Search Implementation

1. **Entity Indexing**: Automatic indexing with `@Indexed` annotation
2. **Field Mapping**: Custom field analyzers and bridges
3. **Query Building**: Programmatic query construction
4. **Result Processing**: Custom result transformation

## Integration Architecture

### External System Integration

1. **Asset Administration Shell (AAS)**
   - Machine lifecycle management
   - Configurable for local development
   - Event-driven integration

2. **Kafka Integration**
   - Asynchronous message processing
   - Event sourcing capabilities
   - Scalable message handling

### API Design

- **RESTful Principles**: Resource-based URLs
- **HTTP Status Codes**: Proper status code usage
- **Content Negotiation**: JSON/XML support
- **Versioning**: API versioning strategy
- **Documentation**: OpenAPI/Swagger integration

## Performance Considerations

### Backend Optimizations

1. **Database Optimization**
   - Connection pooling with HikariCP
   - Query optimization with JPA criteria
   - Second-level caching with EhCache
   - Database indexing strategy

2. **Application Performance**
   - Lazy loading for associations
   - Pagination for large datasets
   - Asynchronous processing
   - Caching strategies

### Frontend Optimizations

1. **Angular Optimizations**
   - OnPush change detection strategy
   - Lazy loading of feature modules
   - Tree shaking for bundle optimization
   - Service worker for caching

2. **Network Optimizations**
   - HTTP interceptors for caching
   - Request debouncing
   - Pagination and virtual scrolling
   - Image optimization

## Scalability Architecture

### Horizontal Scaling

- **Stateless Design**: No server-side session state
- **Load Balancing**: Multiple backend instances
- **Database Scaling**: Read replicas and sharding
- **Caching Layer**: Redis for distributed caching

### Monitoring and Observability

- **Health Checks**: Spring Boot Actuator endpoints
- **Metrics**: Application and business metrics
- **Logging**: Structured logging with correlation IDs
- **Tracing**: Distributed tracing capabilities

## Extension Points

### Adding New Features

1. **New Entity Types**
   - Create JPA entity
   - Add repository interface
   - Implement service layer
   - Create REST controller
   - Add frontend components

2. **Custom Search Implementations**
   - Extend search service interfaces
   - Implement custom query builders
   - Add search result transformers

3. **Integration Points**
   - Event listeners for domain events
   - Custom authentication providers
   - External API integrations
   - Custom validation rules

This architecture provides a solid foundation for extending the CoTwin platform while maintaining code quality, performance, and maintainability.

## Development Guidelines

### Code Quality Standards

1. **Java Backend**
   - Follow Spring Boot best practices
   - Use Lombok to reduce boilerplate code
   - Implement proper exception handling
   - Write comprehensive unit tests
   - Use meaningful variable and method names
   - Document complex business logic

2. **Angular Frontend**
   - Follow Angular style guide
   - Use TypeScript strict mode
   - Implement proper error handling
   - Write unit tests for components and services
   - Use reactive programming patterns
   - Optimize for performance

3. **Database**
   - Use meaningful table and column names
   - Implement proper indexing
   - Write migration scripts for schema changes
   - Document complex queries
   - Follow normalization principles

### Testing Strategy

1. **Unit Tests**
   - Service layer testing with mocks
   - Repository testing with @DataJpaTest
   - Component testing with TestBed
   - Minimum 80% code coverage

2. **Integration Tests**
   - API endpoint testing
   - Database integration testing
   - End-to-end testing with Cypress

3. **Performance Testing**
   - Load testing for critical endpoints
   - Database query performance testing
   - Frontend performance auditing
