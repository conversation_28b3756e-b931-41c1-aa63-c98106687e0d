# CoTwin API Documentation

## Overview

The CoTwin API is a RESTful web service that provides comprehensive access to all platform functionality. The API follows REST principles and uses JSON for data exchange.

## Base URL

- **Development**: `http://localhost:8080/api`
- **Production**: `https://your-domain.com/api`

## Authentication

### JWT Token Authentication

All API endpoints (except authentication) require a valid JWT token in the Authorization header:

```http
Authorization: Bearer <jwt-token>
```

### Authentication Endpoint

#### POST /authenticate
Authenticate user and receive JWT token.

**Request:**
```json
{
  "username": "admin",
  "password": "admin"
}
```

**Response:**
```json
{
  "idToken": "eyJhbGciOiJIUzUxMiJ9...",
  "user": {
    "id": 1,
    "login": "admin",
    "firstName": "Administrator",
    "lastName": "User",
    "email": "<EMAIL>",
    "authorities": ["ROLE_ADMIN"]
  }
}
```

## Core API Endpoints

### Machine Management

#### GET /machines
Retrieve paginated list of machines with filtering.

**Query Parameters:**
- `page` (int): Page number (0-based)
- `size` (int): Page size (default: 20)
- `sort` (string): Sort criteria (e.g., "name,asc")
- `machineName` (string): Filter by machine name
- `companyId` (long): Filter by company ID
- `responsibleUserId` (long): Filter by responsible user
- `machinePhase` (string): Filter by machine phase
- `machineStatus` (string): Filter by machine status

**Response:**
```json
{
  "_embedded": {
    "machineDtoes": [
      {
        "id": 1,
        "name": "Production Line A",
        "description": "Main production line",
        "machinePhase": {
          "phase": "BuildPhase",
          "status": "CONFIGURED"
        },
        "company": {
          "id": 1,
          "name": "ACME Manufacturing"
        },
        "responsibleUser": {
          "id": 2,
          "login": "engineer1"
        },
        "tags": [
          {
            "id": 1,
            "name": "Production",
            "color": "#007bff"
          }
        ]
      }
    ]
  },
  "page": {
    "size": 20,
    "totalElements": 1,
    "totalPages": 1,
    "number": 0
  }
}
```

#### POST /machines
Create a new machine.

**Request (multipart/form-data):**
```json
{
  "name": "New Machine",
  "description": "Machine description",
  "machinePhase": {
    "phase": "PlanPhase",
    "status": "NEWLY_ADDED"
  },
  "companyId": 1,
  "responsibleUserId": 2,
  "tagIds": [1, 2]
}
```

#### GET /machines/{id}
Retrieve specific machine by ID.

#### PUT /machines/{id}
Update existing machine.

#### DELETE /machines/{id}
Delete machine by ID.

#### GET /machines/search
Full-text search across machines.

**Query Parameters:**
- `query` (string): Search query
- `page`, `size`, `sort`: Pagination parameters

### Company Management

#### GET /companies
Retrieve paginated list of companies.

#### POST /companies
Create a new company.

**Request:**
```json
{
  "name": "New Company",
  "description": "Company description",
  "website": "https://example.com",
  "contactPerson": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phoneNumber": "******-0123"
  }
}
```

#### GET /companies/{id}
Retrieve specific company by ID.

#### PUT /companies/{id}
Update existing company.

#### DELETE /companies/{id}
Delete company by ID.

### Person Management (New Feature)

#### GET /persons
Retrieve paginated list of persons with filtering.

**Query Parameters:**
- `name` (string): Filter by first or last name
- `email` (string): Filter by email
- `jobTitle` (string): Filter by job title
- `department` (string): Filter by department
- `companyId` (long): Filter by company ID
- `active` (boolean): Filter by active status

**Response:**
```json
{
  "_embedded": {
    "personDtoes": [
      {
        "id": 1,
        "firstName": "John",
        "lastName": "Doe",
        "email": "<EMAIL>",
        "phoneNumber": "+49-**********",
        "mobileNumber": "+49-**********",
        "jobTitle": "Software Engineer",
        "department": "IT",
        "active": true,
        "fullName": "John Doe",
        "displayName": "John Doe - Software Engineer (ACME Corp)",
        "company": {
          "id": 1,
          "name": "ACME Corp"
        },
        "address": {
          "street": "Main Street 123",
          "postcode": "12345",
          "city": "Berlin",
          "country": "Germany"
        },
        "tags": [
          {
            "id": 1,
            "name": "Developer",
            "color": "#28a745"
          }
        ]
      }
    ]
  }
}
```

#### POST /persons
Create a new person.

**Request:**
```json
{
  "firstName": "Jane",
  "lastName": "Smith",
  "email": "<EMAIL>",
  "phoneNumber": "+49-**********",
  "jobTitle": "Project Manager",
  "department": "Operations",
  "companyId": 1,
  "address": {
    "street": "Example Street 456",
    "postcode": "54321",
    "city": "Munich",
    "country": "Germany"
  },
  "tagIds": [2, 3]
}
```

#### GET /persons/search
Full-text search across persons.

#### GET /persons/by-company/{companyId}
Get persons by company ID.

### Forum System

#### GET /threads
Retrieve paginated list of discussion threads.

#### POST /threads
Create a new discussion thread.

**Request:**
```json
{
  "title": "Discussion Topic",
  "body": "Thread content..."
}
```

#### GET /threads/{id}/posts
Get posts for a specific thread.

#### POST /threads/{threadId}/posts
Create a new post in a thread.

**Request:**
```json
{
  "title": "Reply Title",
  "body": "Post content..."
}
```

#### GET /threads/search
Search threads and posts.

### Tag Management

#### GET /tags
Retrieve all tags.

#### POST /tags
Create a new tag.

**Request:**
```json
{
  "name": "New Tag",
  "color": "#ff6b6b"
}
```

## Error Handling

### HTTP Status Codes

- `200 OK`: Successful GET, PUT requests
- `201 Created`: Successful POST requests
- `204 No Content`: Successful DELETE requests
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Missing or invalid authentication
- `403 Forbidden`: Insufficient permissions
- `404 Not Found`: Resource not found
- `409 Conflict`: Resource conflict (e.g., duplicate email)
- `422 Unprocessable Entity`: Validation errors
- `500 Internal Server Error`: Server error

### Error Response Format

```json
{
  "timestamp": "2023-08-26T14:30:00Z",
  "status": 400,
  "error": "Bad Request",
  "message": "Validation failed",
  "path": "/api/persons",
  "details": [
    {
      "field": "email",
      "message": "Email already exists"
    }
  ]
}
```

## Pagination

All list endpoints support pagination with the following parameters:

- `page`: Page number (0-based, default: 0)
- `size`: Page size (default: 20, max: 100)
- `sort`: Sort criteria (format: "property,direction")

Example: `GET /api/machines?page=0&size=10&sort=name,asc`

## Filtering and Search

### Filtering
Most list endpoints support filtering through query parameters that match entity properties.

### Full-text Search
Search endpoints (`/search`) provide full-text search capabilities across multiple fields using Elasticsearch.

## Rate Limiting

- **Development**: No rate limiting
- **Production**: 1000 requests per hour per user

## API Versioning

The API uses URL versioning:
- Current version: `/api/v1/` (default `/api/` maps to v1)
- Future versions: `/api/v2/`, etc.

## OpenAPI/Swagger Documentation

Interactive API documentation is available at:
- **Development**: `http://localhost:8080/swagger-ui.html`
- **OpenAPI Spec**: `http://localhost:8080/v3/api-docs`

## SDK and Client Libraries

Currently, the API is consumed directly via HTTP requests. Future releases may include:
- JavaScript/TypeScript SDK
- Java client library
- Python client library

## Webhooks (Future Feature)

Planned webhook support for:
- Machine status changes
- New forum posts
- User registrations

## Best Practices

1. **Always include Authorization header** for authenticated endpoints
2. **Use appropriate HTTP methods** (GET for retrieval, POST for creation, etc.)
3. **Handle pagination** for list endpoints
4. **Implement proper error handling** for all status codes
5. **Use filtering and search** to reduce data transfer
6. **Cache responses** where appropriate
7. **Validate input data** before sending requests

## Examples

### Complete Machine Creation Flow

```javascript
// 1. Authenticate
const authResponse = await fetch('/api/authenticate', {
  method: 'POST',
  headers: { 'Content-Type': 'application/json' },
  body: JSON.stringify({ username: 'admin', password: 'admin' })
});
const { idToken } = await authResponse.json();

// 2. Create machine
const machineData = new FormData();
machineData.append('request', JSON.stringify({
  name: 'New Production Line',
  description: 'Automated production line',
  machinePhase: { phase: 'PlanPhase', status: 'NEWLY_ADDED' },
  companyId: 1
}));

const machineResponse = await fetch('/api/machines', {
  method: 'POST',
  headers: { 'Authorization': `Bearer ${idToken}` },
  body: machineData
});
const machine = await machineResponse.json();
```

This API provides comprehensive access to all CoTwin platform functionality with proper authentication, error handling, and documentation.
