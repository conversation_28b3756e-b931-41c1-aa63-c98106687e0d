package de.iotiq.cotwinbackend.messages.request;

import de.iotiq.cotwinbackend.domain.Note;
import lombok.Data;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * Request DTO for creating and updating notes.
 */
@Data
public class SaveNoteRequest {
    
    @NotNull
    @Size(min = 1, max = 100)
    private String title;
    
    @Size(max = 1000)
    private String content;
    
    private Note.NotePriority priority = Note.NotePriority.MEDIUM;
    
    private Boolean completed = false;
    
    @Size(max = 50)
    private String category;
}
